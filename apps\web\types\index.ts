// User and Authentication Types
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  createdAt: Date;
  updatedAt: Date;
  profile: UserProfile;
}

export interface UserProfile {
  id: string;
  userId: string;
  currentXP: number;
  totalXP: number;
  level: number;
  streak: number;
  lastActiveDate: Date;
  selectedCareerPath?: string;
  completedMilestones: string[];
  achievements: Achievement[];
}

// Milestone and Journey Types
export interface Milestone {
  id: string;
  title: string;
  description: string;
  order: number;
  status: MilestoneStatus;
  xpReward: number;
  estimatedDuration: number; // in minutes
  prerequisites: string[];
  tasks: Task[];
  category: MilestoneCategory;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
}

export type MilestoneStatus = 'locked' | 'unlocked' | 'active' | 'completed';

export type MilestoneCategory = 
  | 'self-discovery' 
  | 'career-exploration' 
  | 'skill-building' 
  | 'networking' 
  | 'job-search' 
  | 'interview-prep';

// Task Types
export interface Task {
  id: string;
  milestoneId: string;
  title: string;
  description: string;
  type: TaskType;
  order: number;
  status: TaskStatus;
  xpReward: number;
  estimatedDuration: number;
  content: TaskContent;
  submissions?: TaskSubmission[];
  feedback?: TaskFeedback[];
  createdAt: Date;
  updatedAt: Date;
}

export type TaskType = 
  | 'quiz' 
  | 'reflection' 
  | 'upload' 
  | 'peer-review' 
  | 'chat' 
  | 'form' 
  | 'research';

export type TaskStatus = 'not_started' | 'in_progress' | 'submitted' | 'reviewed' | 'completed';

export interface TaskContent {
  instructions: string;
  questions?: QuizQuestion[] | ReflectionPrompt[];
  uploadRequirements?: UploadRequirements;
  formFields?: FormField[];
  chatPrompts?: ChatPrompt[];
}

export interface QuizQuestion {
  id: string;
  question: string;
  type: 'multiple_choice' | 'single_choice' | 'true_false' | 'text';
  options?: string[];
  correctAnswer?: string | string[];
  explanation?: string;
  points: number;
}

export interface ReflectionPrompt {
  id: string;
  prompt: string;
  minWords?: number;
  maxWords?: number;
  guidelines: string[];
}

export interface UploadRequirements {
  allowedTypes: string[];
  maxSize: number; // in MB
  maxFiles: number;
  description: string;
  examples?: string[];
}

export interface FormField {
  id: string;
  label: string;
  type: 'text' | 'textarea' | 'select' | 'multiselect' | 'date' | 'number';
  required: boolean;
  options?: string[];
  validation?: FieldValidation;
  placeholder?: string;
  helperText?: string;
}

export interface FieldValidation {
  min?: number;
  max?: number;
  pattern?: string;
  message?: string;
}

export interface ChatPrompt {
  id: string;
  agentType: AgentType;
  initialMessage: string;
  context: string;
  objectives: string[];
}

// Agent and Chat Types
export type AgentType = 'coach' | 'designer' | 'mentor' | 'buddy';

export interface Agent {
  id: string;
  type: AgentType;
  name: string;
  avatar: string;
  description: string;
  personality: string[];
  specialties: string[];
  isActive: boolean;
}

export interface ChatMessage {
  id: string;
  chatId: string;
  agentId?: string;
  userId?: string;
  content: string;
  type: 'text' | 'action' | 'system';
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface ChatSession {
  id: string;
  userId: string;
  agentId: string;
  taskId?: string;
  milestoneId?: string;
  status: 'active' | 'completed' | 'archived';
  messages: ChatMessage[];
  context: ChatContext;
  createdAt: Date;
  updatedAt: Date;
}

export interface ChatContext {
  currentMilestone?: string;
  currentTask?: string;
  userGoals: string[];
  conversationObjectives: string[];
  previousSessions: string[];
}

// Career and Role Types
export interface CareerRole {
  id: string;
  title: string;
  description: string;
  industry: string;
  level: 'entry' | 'mid' | 'senior' | 'executive';
  salaryRange: SalaryRange;
  skills: Skill[];
  responsibilities: string[];
  qualifications: string[];
  growthPath: string[];
  demandScore: number; // 1-10
  satisfactionScore: number; // 1-10
  workLifeBalance: number; // 1-10
  remoteOptions: boolean;
  tags: string[];
  relatedRoles: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface SalaryRange {
  min: number;
  max: number;
  currency: string;
  period: 'hourly' | 'monthly' | 'yearly';
  location: string;
}

export interface Skill {
  id: string;
  name: string;
  category: SkillCategory;
  level: SkillLevel;
  importance: 'low' | 'medium' | 'high' | 'critical';
  description?: string;
  resources?: LearningResource[];
}

export type SkillCategory = 
  | 'technical' 
  | 'soft' 
  | 'leadership' 
  | 'communication' 
  | 'analytical' 
  | 'creative';

export type SkillLevel = 'beginner' | 'intermediate' | 'advanced' | 'expert';

export interface LearningResource {
  id: string;
  title: string;
  type: 'course' | 'book' | 'article' | 'video' | 'certification';
  url?: string;
  provider: string;
  duration?: number;
  cost?: number;
  rating?: number;
  description: string;
}

// Achievement and Gamification Types
export interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  category: AchievementCategory;
  rarity: 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary';
  xpReward: number;
  conditions: AchievementCondition[];
  unlockedAt?: Date;
  progress?: AchievementProgress;
}

export type AchievementCategory = 
  | 'milestone' 
  | 'streak' 
  | 'social' 
  | 'skill' 
  | 'speed' 
  | 'exploration' 
  | 'special';

export interface AchievementCondition {
  type: 'milestone_count' | 'streak_days' | 'xp_total' | 'task_type' | 'time_based';
  value: number | string;
  operator: 'equals' | 'greater_than' | 'less_than' | 'contains';
}

export interface AchievementProgress {
  current: number;
  target: number;
  percentage: number;
}

// Submission and Feedback Types
export interface TaskSubmission {
  id: string;
  taskId: string;
  userId: string;
  content: SubmissionContent;
  status: 'submitted' | 'under_review' | 'approved' | 'needs_revision';
  submittedAt: Date;
  reviewedAt?: Date;
  score?: number;
  feedback?: TaskFeedback[];
}

export interface SubmissionContent {
  answers?: Record<string, any>;
  files?: ProjectFile[];
  text?: string;
  metadata?: Record<string, any>;
}

export interface TaskFeedback {
  id: string;
  submissionId: string;
  reviewerId: string;
  reviewerType: 'mentor' | 'peer' | 'ai' | 'system';
  type: 'general' | 'specific' | 'suggestion' | 'correction';
  content: string;
  rating?: number;
  helpful?: boolean;
  createdAt: Date;
}

export interface ProjectFile {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  thumbnail?: string;
  uploadedAt: Date;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: ApiError;
  message?: string;
  meta?: ResponseMeta;
}

export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, any>;
  field?: string;
}

export interface ResponseMeta {
  page?: number;
  limit?: number;
  total?: number;
  hasMore?: boolean;
}
