{"version": 3, "sources": ["../../../../../../src/server/route-modules/app-page/vendored/ssr/react-server-dom-webpack-client-edge.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactServerDOMWebpackClientEdge\n"], "names": ["module", "exports", "require", "vendored", "ReactServerDOMWebpackClientEdge"], "mappings": "AAAAA,OAAOC,OAAO,GAAGC,QAAQ,yBAAyBC,QAAQ,CACxD,YACD,CAACC,+BAA+B"}