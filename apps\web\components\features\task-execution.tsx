'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@workspace/ui/components/card';
import { Button } from '@workspace/ui/components/button';
import { Input } from '@workspace/ui/components/input';
import { Textarea } from '@workspace/ui/components/textarea';
import { Badge } from '@workspace/ui/components/badge';
import { Progress } from '@workspace/ui/components/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@workspace/ui/components/tabs';
import { 
  CheckCircle, 
  Clock, 
  Trophy, 
  Upload, 
  MessageSquare, 
  FileText, 
  Users,
  AlertCircle 
} from 'lucide-react';
import { Task, TaskType, QuizQuestion, ReflectionPrompt } from '../../types';
import { useMilestone } from '../../lib/contexts/milestone-context';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

interface TaskExecutionProps {
  task: Task;
  className?: string;
}

const taskTypeConfig = {
  quiz: {
    icon: CheckCircle,
    color: 'bg-blue-50 border-blue-200',
    badgeColor: 'bg-blue-100 text-blue-800',
    title: 'Quiz',
  },
  reflection: {
    icon: FileText,
    color: 'bg-purple-50 border-purple-200',
    badgeColor: 'bg-purple-100 text-purple-800',
    title: 'Reflection',
  },
  upload: {
    icon: Upload,
    color: 'bg-green-50 border-green-200',
    badgeColor: 'bg-green-100 text-green-800',
    title: 'Upload',
  },
  'peer-review': {
    icon: Users,
    color: 'bg-orange-50 border-orange-200',
    badgeColor: 'bg-orange-100 text-orange-800',
    title: 'Peer Review',
  },
  chat: {
    icon: MessageSquare,
    color: 'bg-pink-50 border-pink-200',
    badgeColor: 'bg-pink-100 text-pink-800',
    title: 'Chat',
  },
  form: {
    icon: FileText,
    color: 'bg-indigo-50 border-indigo-200',
    badgeColor: 'bg-indigo-100 text-indigo-800',
    title: 'Form',
  },
  research: {
    icon: FileText,
    color: 'bg-teal-50 border-teal-200',
    badgeColor: 'bg-teal-100 text-teal-800',
    title: 'Research',
  },
};

const submissionSchema = z.object({
  answers: z.record(z.any()).optional(),
  text: z.string().optional(),
  files: z.array(z.any()).optional(),
});

export function TaskExecution({ task, className }: TaskExecutionProps) {
  const { updateTaskStatus, submitTask } = useMilestone();
  const [currentStep, setCurrentStep] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [answers, setAnswers] = useState<Record<string, any>>({});
  const [reflectionText, setReflectionText] = useState('');
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);

  const config = taskTypeConfig[task.type];
  const TaskIcon = config.icon;

  const { register, handleSubmit, formState: { errors } } = useForm({
    resolver: zodResolver(submissionSchema),
  });

  const handleStartTask = async () => {
    if (task.status === 'not_started') {
      await updateTaskStatus(task.id, 'in_progress');
    }
  };

  const handleSubmitTask = async () => {
    setIsSubmitting(true);
    try {
      const submission = {
        answers: task.type === 'quiz' ? answers : undefined,
        text: task.type === 'reflection' ? reflectionText : undefined,
        files: uploadedFiles.length > 0 ? uploadedFiles : undefined,
        metadata: {
          completedAt: new Date(),
          timeSpent: 0, // Track this in real implementation
        },
      };

      await submitTask(task.id, submission);
    } catch (error) {
      console.error('Failed to submit task:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderQuizContent = () => {
    const questions = task.content.questions as QuizQuestion[];
    if (!questions) return null;

    return (
      <div className="space-y-6">
        {questions.map((question, index) => (
          <Card key={question.id} className="p-4">
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <Badge variant="outline" className="mt-1">
                  {index + 1}
                </Badge>
                <div className="flex-1">
                  <h4 className="font-medium">{question.question}</h4>
                  <p className="text-sm text-muted-foreground mt-1">
                    {question.points} points
                  </p>
                </div>
              </div>

              <div className="space-y-2">
                {question.type === 'multiple_choice' || question.type === 'single_choice' ? (
                  question.options?.map((option, optionIndex) => (
                    <label key={optionIndex} className="flex items-center gap-2 cursor-pointer">
                      <input
                        type={question.type === 'multiple_choice' ? 'checkbox' : 'radio'}
                        name={question.id}
                        value={option}
                        onChange={(e) => {
                          if (question.type === 'multiple_choice') {
                            const currentAnswers = answers[question.id] || [];
                            if (e.target.checked) {
                              setAnswers({
                                ...answers,
                                [question.id]: [...currentAnswers, option],
                              });
                            } else {
                              setAnswers({
                                ...answers,
                                [question.id]: currentAnswers.filter((a: string) => a !== option),
                              });
                            }
                          } else {
                            setAnswers({
                              ...answers,
                              [question.id]: option,
                            });
                          }
                        }}
                        className="rounded"
                      />
                      <span className="text-sm">{option}</span>
                    </label>
                  ))
                ) : question.type === 'true_false' ? (
                  <div className="flex gap-4">
                    <label className="flex items-center gap-2 cursor-pointer">
                      <input
                        type="radio"
                        name={question.id}
                        value="true"
                        onChange={() => setAnswers({ ...answers, [question.id]: 'true' })}
                        className="rounded"
                      />
                      <span className="text-sm">True</span>
                    </label>
                    <label className="flex items-center gap-2 cursor-pointer">
                      <input
                        type="radio"
                        name={question.id}
                        value="false"
                        onChange={() => setAnswers({ ...answers, [question.id]: 'false' })}
                        className="rounded"
                      />
                      <span className="text-sm">False</span>
                    </label>
                  </div>
                ) : (
                  <Textarea
                    placeholder="Enter your answer..."
                    value={answers[question.id] || ''}
                    onChange={(e) => setAnswers({ ...answers, [question.id]: e.target.value })}
                    className="min-h-[100px]"
                  />
                )}
              </div>
            </div>
          </Card>
        ))}
      </div>
    );
  };

  const renderReflectionContent = () => {
    const prompts = task.content.questions as ReflectionPrompt[];
    if (!prompts) return null;

    return (
      <div className="space-y-6">
        {prompts.map((prompt, index) => (
          <Card key={prompt.id} className="p-4">
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <Badge variant="outline" className="mt-1">
                  {index + 1}
                </Badge>
                <div className="flex-1">
                  <h4 className="font-medium">{prompt.prompt}</h4>
                  {prompt.minWords && (
                    <p className="text-sm text-muted-foreground mt-1">
                      Minimum {prompt.minWords} words
                      {prompt.maxWords && `, maximum ${prompt.maxWords} words`}
                    </p>
                  )}
                </div>
              </div>

              {prompt.guidelines.length > 0 && (
                <div className="bg-muted p-3 rounded-lg">
                  <h5 className="font-medium text-sm mb-2">Guidelines:</h5>
                  <ul className="text-sm space-y-1">
                    {prompt.guidelines.map((guideline, i) => (
                      <li key={i} className="flex items-start gap-2">
                        <span className="text-muted-foreground">•</span>
                        <span>{guideline}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              <Textarea
                placeholder="Share your thoughts and reflections..."
                value={reflectionText}
                onChange={(e) => setReflectionText(e.target.value)}
                className="min-h-[200px]"
              />

              <div className="text-sm text-muted-foreground">
                Word count: {reflectionText.split(/\s+/).filter(word => word.length > 0).length}
              </div>
            </div>
          </Card>
        ))}
      </div>
    );
  };

  const renderUploadContent = () => {
    const requirements = task.content.uploadRequirements;
    if (!requirements) return null;

    return (
      <Card className="p-6">
        <div className="space-y-4">
          <div>
            <h4 className="font-medium mb-2">Upload Requirements</h4>
            <p className="text-sm text-muted-foreground">{requirements.description}</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="font-medium">Allowed types:</span>
              <p className="text-muted-foreground">{requirements.allowedTypes.join(', ')}</p>
            </div>
            <div>
              <span className="font-medium">Max size:</span>
              <p className="text-muted-foreground">{requirements.maxSize} MB</p>
            </div>
            <div>
              <span className="font-medium">Max files:</span>
              <p className="text-muted-foreground">{requirements.maxFiles}</p>
            </div>
          </div>

          {requirements.examples && requirements.examples.length > 0 && (
            <div>
              <h5 className="font-medium text-sm mb-2">Examples:</h5>
              <ul className="text-sm space-y-1">
                {requirements.examples.map((example, i) => (
                  <li key={i} className="flex items-start gap-2">
                    <span className="text-muted-foreground">•</span>
                    <span>{example}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center">
            <Upload className="h-8 w-8 mx-auto mb-4 text-muted-foreground" />
            <p className="text-sm text-muted-foreground mb-4">
              Drag and drop your files here, or click to browse
            </p>
            <Button variant="outline" size="sm">
              Choose Files
            </Button>
          </div>

          {uploadedFiles.length > 0 && (
            <div className="space-y-2">
              <h5 className="font-medium text-sm">Uploaded Files:</h5>
              {uploadedFiles.map((file, index) => (
                <div key={index} className="flex items-center justify-between p-2 bg-muted rounded">
                  <span className="text-sm">{file.name}</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setUploadedFiles(files => files.filter((_, i) => i !== index))}
                  >
                    Remove
                  </Button>
                </div>
              ))}
            </div>
          )}
        </div>
      </Card>
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'not_started':
        return 'bg-gray-100 text-gray-800';
      case 'in_progress':
        return 'bg-yellow-100 text-yellow-800';
      case 'submitted':
        return 'bg-blue-100 text-blue-800';
      case 'reviewed':
        return 'bg-purple-100 text-purple-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={className}
    >
      <Card className={`${config.color}`}>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-3">
              <TaskIcon className="h-6 w-6" />
              <div>
                <CardTitle className="text-xl">{task.title}</CardTitle>
                <CardDescription>{task.description}</CardDescription>
              </div>
            </div>
            <div className="flex gap-2">
              <Badge variant="outline" className={config.badgeColor}>
                {config.title}
              </Badge>
              <Badge variant="outline" className={getStatusColor(task.status)}>
                {task.status.replace('_', ' ').toUpperCase()}
              </Badge>
            </div>
          </div>

          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <div className="flex items-center gap-1">
              <Clock className="h-4 w-4" />
              <span>{task.estimatedDuration} min</span>
            </div>
            <div className="flex items-center gap-1">
              <Trophy className="h-4 w-4" />
              <span>{task.xpReward} XP</span>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Instructions */}
          <div className="bg-muted p-4 rounded-lg">
            <h4 className="font-medium mb-2">Instructions</h4>
            <p className="text-sm">{task.content.instructions}</p>
          </div>

          {/* Task Content */}
          {task.status === 'not_started' ? (
            <div className="text-center py-8">
              <AlertCircle className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <h3 className="font-medium mb-2">Ready to start?</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Click the button below to begin this task.
              </p>
              <Button onClick={handleStartTask}>
                Start Task
              </Button>
            </div>
          ) : (
            <>
              {task.type === 'quiz' && renderQuizContent()}
              {task.type === 'reflection' && renderReflectionContent()}
              {task.type === 'upload' && renderUploadContent()}

              {/* Submit Button */}
              {task.status === 'in_progress' && (
                <div className="flex justify-end pt-4 border-t">
                  <Button
                    onClick={handleSubmitTask}
                    disabled={isSubmitting}
                    className="min-w-[120px]"
                  >
                    {isSubmitting ? 'Submitting...' : 'Submit Task'}
                  </Button>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}
