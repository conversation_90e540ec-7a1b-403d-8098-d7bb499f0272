'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@workspace/ui/components/card';
import { But<PERSON> } from '@workspace/ui/components/button';
import { Badge } from '@workspace/ui/components/badge';
import { Progress } from '@workspace/ui/components/progress';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from '@workspace/ui/components/tabs';
import { 
  Target, 
  Map, 
  Filter, 
  CheckCircle, 
  Clock, 
  Lock,
  Play,
  Trophy,
  ArrowRight
} from 'lucide-react';
import { MilestoneCard } from '../../../../components/features/milestone-card';
import { TaskExecution } from '../../../../components/features/task-execution';
import { useMilestone } from '../../../../lib/contexts/milestone-context';
import { Milestone, Task } from '../../../../types';

// Mock data - same as dashboard
const mockMilestones: Milestone[] = [
  {
    id: '1',
    title: 'Self-Discovery Foundation',
    description: 'Understand your values, strengths, and career interests through guided assessments and reflections.',
    order: 1,
    status: 'completed',
    xpReward: 200,
    estimatedDuration: 45,
    prerequisites: [],
    tasks: [
      {
        id: '1-1',
        milestoneId: '1',
        title: 'Values Assessment',
        description: 'Identify your core values through a comprehensive assessment',
        type: 'quiz',
        order: 1,
        status: 'completed',
        xpReward: 50,
        estimatedDuration: 15,
        content: { 
          instructions: 'Complete this assessment to understand what matters most to you in your career.',
          questions: [
            {
              id: 'q1',
              question: 'What motivates you most in your work?',
              type: 'single_choice',
              options: ['Making a difference', 'Financial success', 'Creative expression', 'Leadership opportunities'],
              points: 10,
            }
          ]
        },
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: '1-2',
        milestoneId: '1',
        title: 'Strengths Reflection',
        description: 'Reflect on your key strengths and how they apply to your career',
        type: 'reflection',
        order: 2,
        status: 'completed',
        xpReward: 50,
        estimatedDuration: 20,
        content: { 
          instructions: 'Take time to reflect on your strengths and how they can be applied in your career.',
          questions: [
            {
              id: 'r1',
              prompt: 'Describe three of your greatest strengths and provide examples of how you\'ve used them.',
              minWords: 100,
              maxWords: 500,
              guidelines: [
                'Be specific with examples',
                'Consider both technical and soft skills',
                'Think about feedback you\'ve received from others'
              ]
            }
          ]
        },
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ],
    category: 'self-discovery',
    difficulty: 'beginner',
    tags: ['assessment', 'reflection', 'foundation'],
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: '2',
    title: 'Career Exploration',
    description: 'Explore different career paths and understand industry trends and opportunities.',
    order: 2,
    status: 'active',
    xpReward: 300,
    estimatedDuration: 60,
    prerequisites: ['1'],
    tasks: [
      {
        id: '2-1',
        milestoneId: '2',
        title: 'Industry Research',
        description: 'Research your target industry trends and opportunities',
        type: 'research',
        order: 1,
        status: 'completed',
        xpReward: 75,
        estimatedDuration: 30,
        content: { 
          instructions: 'Research current trends, growth opportunities, and challenges in your target industry.'
        },
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: '2-2',
        milestoneId: '2',
        title: 'Role Comparison',
        description: 'Compare different roles in your field of interest',
        type: 'form',
        order: 2,
        status: 'in_progress',
        xpReward: 75,
        estimatedDuration: 30,
        content: { 
          instructions: 'Use our role comparison tool to evaluate different career options.',
          formFields: [
            {
              id: 'role1',
              label: 'Primary Role of Interest',
              type: 'text',
              required: true,
              placeholder: 'e.g., Software Engineer'
            },
            {
              id: 'role2',
              label: 'Alternative Role',
              type: 'text',
              required: true,
              placeholder: 'e.g., Product Manager'
            }
          ]
        },
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ],
    category: 'career-exploration',
    difficulty: 'beginner',
    tags: ['research', 'exploration', 'industry'],
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: '3',
    title: 'Skill Building Plan',
    description: 'Create a personalized learning roadmap to develop the skills needed for your target role.',
    order: 3,
    status: 'unlocked',
    xpReward: 400,
    estimatedDuration: 90,
    prerequisites: ['2'],
    tasks: [],
    category: 'skill-building',
    difficulty: 'intermediate',
    tags: ['skills', 'learning', 'roadmap'],
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: '4',
    title: 'Network Building',
    description: 'Build professional relationships and expand your network in your target industry.',
    order: 4,
    status: 'locked',
    xpReward: 350,
    estimatedDuration: 75,
    prerequisites: ['3'],
    tasks: [],
    category: 'networking',
    difficulty: 'intermediate',
    tags: ['networking', 'relationships', 'industry'],
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

export default function MilestonesPage() {
  const { milestones, currentMilestone } = useMilestone();
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'roadmap'>('grid');

  // Use mock data for now
  const displayMilestones = milestones.length > 0 ? milestones : mockMilestones;
  
  const completedCount = displayMilestones.filter(m => m.status === 'completed').length;
  const activeCount = displayMilestones.filter(m => m.status === 'active').length;
  const unlockedCount = displayMilestones.filter(m => m.status === 'unlocked').length;
  const lockedCount = displayMilestones.filter(m => m.status === 'locked').length;

  const overallProgress = (completedCount / displayMilestones.length) * 100;

  const activeTasks = displayMilestones
    .filter(m => m.status === 'active' || m.status === 'unlocked')
    .flatMap(m => m.tasks)
    .filter(t => t.status === 'in_progress' || t.status === 'not_started');

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Learning Milestones</h1>
          <p className="text-gray-600 mt-2">
            Track your progress through your personalized career journey
          </p>
        </div>
        <div className="flex gap-3">
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
          <Button variant="outline" size="sm">
            <Map className="h-4 w-4 mr-2" />
            Roadmap View
          </Button>
        </div>
      </div>

      {/* Progress Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Trophy className="h-5 w-5 text-yellow-600" />
            Your Progress
          </CardTitle>
          <CardDescription>
            Overall completion across all milestones
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Overall Progress</span>
              <span className="text-sm text-muted-foreground">
                {completedCount} of {displayMilestones.length} completed
              </span>
            </div>
            <Progress value={overallProgress} className="h-3" variant="milestone" />
            
            <div className="grid grid-cols-4 gap-4 mt-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{completedCount}</div>
                <div className="text-sm text-gray-600">Completed</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{activeCount}</div>
                <div className="text-sm text-gray-600">Active</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">{unlockedCount}</div>
                <div className="text-sm text-gray-600">Unlocked</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-400">{lockedCount}</div>
                <div className="text-sm text-gray-600">Locked</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content */}
      <Tabs defaultValue="milestones" className="space-y-6">
        <TabsList>
          <TabsTrigger value="milestones">All Milestones</TabsTrigger>
          <TabsTrigger value="active">Active Tasks</TabsTrigger>
          <TabsTrigger value="roadmap">Roadmap</TabsTrigger>
        </TabsList>

        <TabsContent value="milestones" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {displayMilestones.map((milestone, index) => (
              <motion.div
                key={milestone.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <MilestoneCard milestone={milestone} />
              </motion.div>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="active" className="space-y-6">
          {activeTasks.length > 0 ? (
            <div className="space-y-6">
              {activeTasks.map((task) => (
                <motion.div
                  key={task.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                >
                  <TaskExecution task={task} />
                </motion.div>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-500" />
                <h3 className="font-medium mb-2">All caught up!</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  You don't have any active tasks right now.
                </p>
                <Button>
                  <ArrowRight className="h-4 w-4 mr-2" />
                  Start Next Milestone
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="roadmap" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Map className="h-5 w-5 text-blue-600" />
                Learning Roadmap
              </CardTitle>
              <CardDescription>
                Visual representation of your learning journey
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-8">
                {displayMilestones.map((milestone, index) => (
                  <div key={milestone.id} className="flex items-start gap-4">
                    <div className="flex flex-col items-center">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        milestone.status === 'completed' ? 'bg-green-100 text-green-600' :
                        milestone.status === 'active' ? 'bg-blue-100 text-blue-600' :
                        milestone.status === 'unlocked' ? 'bg-yellow-100 text-yellow-600' :
                        'bg-gray-100 text-gray-400'
                      }`}>
                        {milestone.status === 'completed' ? (
                          <CheckCircle className="h-4 w-4" />
                        ) : milestone.status === 'active' ? (
                          <Play className="h-4 w-4" />
                        ) : milestone.status === 'unlocked' ? (
                          <Target className="h-4 w-4" />
                        ) : (
                          <Lock className="h-4 w-4" />
                        )}
                      </div>
                      {index < displayMilestones.length - 1 && (
                        <div className="w-0.5 h-16 bg-gray-200 mt-2" />
                      )}
                    </div>
                    <div className="flex-1 pb-8">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="font-medium">{milestone.title}</h3>
                        <Badge variant="outline" className={
                          milestone.status === 'completed' ? 'bg-green-50 text-green-700' :
                          milestone.status === 'active' ? 'bg-blue-50 text-blue-700' :
                          milestone.status === 'unlocked' ? 'bg-yellow-50 text-yellow-700' :
                          'bg-gray-50 text-gray-500'
                        }>
                          {milestone.status}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-3">{milestone.description}</p>
                      <div className="flex items-center gap-4 text-xs text-gray-500">
                        <span className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {milestone.estimatedDuration} min
                        </span>
                        <span className="flex items-center gap-1">
                          <Trophy className="h-3 w-3" />
                          {milestone.xpReward} XP
                        </span>
                        <span>{milestone.tasks.length} tasks</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
