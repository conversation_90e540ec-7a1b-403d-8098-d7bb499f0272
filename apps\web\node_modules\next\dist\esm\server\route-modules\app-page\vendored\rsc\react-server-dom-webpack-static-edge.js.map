{"version": 3, "sources": ["../../../../../../src/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-static-edge.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactServerDOMWebpackStaticEdge\n"], "names": ["module", "exports", "require", "vendored", "ReactServerDOMWebpackStaticEdge"], "mappings": "AAAAA,OAAOC,OAAO,GAAGC,QAAQ,yBAAyBC,QAAQ,CACxD,YACD,CAACC,+BAA+B"}