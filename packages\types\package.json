{"name": "@kaizen/types", "version": "1.0.0", "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": "./src/index.ts"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint src/**/*.ts", "type-check": "tsc --noEmit", "test": "jest", "clean": "rm -rf dist coverage"}, "devDependencies": {"typescript": "^5.2.0", "@kaizen/eslint-config": "workspace:*", "eslint": "^8.48.0", "jest": "^29.7.0"}}