{"version": 3, "sources": ["../../src/build/worker.ts"], "sourcesContent": ["import '../server/require-hook'\n\nexport {\n  getDefinedNamedExports,\n  hasCustomGetInitialProps,\n  isPageStatic,\n} from './utils'\nexport { exportPages } from '../export/worker'\n"], "names": ["getDefinedNamedExports", "hasCustomGetInitialProps", "isPageStatic", "exportPages"], "mappings": "AAAA,OAAO,yBAAwB;AAE/B,SACEA,sBAAsB,EACtBC,wBAAwB,EACxBC,YAAY,QACP,UAAS;AAChB,SAASC,WAAW,QAAQ,mBAAkB"}