'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@workspace/ui/components/card';
import { Badge } from '@workspace/ui/components/badge';
import { <PERSON><PERSON> } from '@workspace/ui/components/button';
import { Input } from '@workspace/ui/components/input';
import { Progress } from '@workspace/ui/components/progress';
import { 
  Search, 
  Filter, 
  Star, 
  DollarSign, 
  MapPin, 
  Clock, 
  TrendingUp,
  Users,
  Heart,
  Home,
  Briefcase
} from 'lucide-react';
import { CareerRole, Skill } from '../../types';

interface CareerRoleBrowserProps {
  className?: string;
}

// Mock data - replace with real data from API
const mockRoles: CareerRole[] = [
  {
    id: '1',
    title: 'Frontend Developer',
    description: 'Build user interfaces and experiences for web applications using modern frameworks and technologies.',
    industry: 'Technology',
    level: 'entry',
    salaryRange: {
      min: 60000,
      max: 90000,
      currency: 'USD',
      period: 'yearly',
      location: 'Remote',
    },
    skills: [
      { id: '1', name: 'React', category: 'technical', level: 'intermediate', importance: 'critical' },
      { id: '2', name: 'JavaScript', category: 'technical', level: 'advanced', importance: 'critical' },
      { id: '3', name: 'CSS', category: 'technical', level: 'intermediate', importance: 'high' },
      { id: '4', name: 'Communication', category: 'soft', level: 'intermediate', importance: 'medium' },
    ],
    responsibilities: [
      'Develop responsive web applications',
      'Collaborate with designers and backend developers',
      'Optimize applications for performance',
      'Write clean, maintainable code',
    ],
    qualifications: [
      'Bachelor\'s degree in Computer Science or related field',
      '2+ years of experience with React',
      'Strong understanding of web technologies',
    ],
    growthPath: ['Senior Frontend Developer', 'Lead Developer', 'Engineering Manager'],
    demandScore: 9,
    satisfactionScore: 8,
    workLifeBalance: 7,
    remoteOptions: true,
    tags: ['React', 'JavaScript', 'Remote', 'Startup'],
    relatedRoles: ['2', '3'],
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: '2',
    title: 'UX Designer',
    description: 'Design intuitive and engaging user experiences for digital products through research and prototyping.',
    industry: 'Design',
    level: 'mid',
    salaryRange: {
      min: 70000,
      max: 110000,
      currency: 'USD',
      period: 'yearly',
      location: 'San Francisco, CA',
    },
    skills: [
      { id: '5', name: 'Figma', category: 'technical', level: 'advanced', importance: 'critical' },
      { id: '6', name: 'User Research', category: 'analytical', level: 'intermediate', importance: 'critical' },
      { id: '7', name: 'Prototyping', category: 'creative', level: 'advanced', importance: 'high' },
      { id: '8', name: 'Empathy', category: 'soft', level: 'advanced', importance: 'high' },
    ],
    responsibilities: [
      'Conduct user research and usability testing',
      'Create wireframes and prototypes',
      'Collaborate with product and engineering teams',
      'Design user flows and information architecture',
    ],
    qualifications: [
      'Bachelor\'s degree in Design or related field',
      '3+ years of UX design experience',
      'Portfolio demonstrating design process',
    ],
    growthPath: ['Senior UX Designer', 'Design Lead', 'Head of Design'],
    demandScore: 8,
    satisfactionScore: 9,
    workLifeBalance: 8,
    remoteOptions: true,
    tags: ['Design', 'Research', 'Figma', 'Remote'],
    relatedRoles: ['1', '3'],
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: '3',
    title: 'Product Manager',
    description: 'Drive product strategy and execution, working cross-functionally to deliver valuable solutions to users.',
    industry: 'Technology',
    level: 'mid',
    salaryRange: {
      min: 90000,
      max: 140000,
      currency: 'USD',
      period: 'yearly',
      location: 'New York, NY',
    },
    skills: [
      { id: '9', name: 'Product Strategy', category: 'analytical', level: 'advanced', importance: 'critical' },
      { id: '10', name: 'Data Analysis', category: 'analytical', level: 'intermediate', importance: 'high' },
      { id: '11', name: 'Leadership', category: 'leadership', level: 'intermediate', importance: 'critical' },
      { id: '12', name: 'Communication', category: 'communication', level: 'advanced', importance: 'critical' },
    ],
    responsibilities: [
      'Define product roadmap and strategy',
      'Gather and prioritize requirements',
      'Work with engineering and design teams',
      'Analyze product metrics and user feedback',
    ],
    qualifications: [
      'Bachelor\'s degree in Business or related field',
      '4+ years of product management experience',
      'Strong analytical and communication skills',
    ],
    growthPath: ['Senior Product Manager', 'Director of Product', 'VP of Product'],
    demandScore: 9,
    satisfactionScore: 8,
    workLifeBalance: 6,
    remoteOptions: false,
    tags: ['Strategy', 'Leadership', 'Analytics', 'B2B'],
    relatedRoles: ['1', '2'],
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

const levelColors = {
  entry: 'bg-green-100 text-green-800',
  mid: 'bg-blue-100 text-blue-800',
  senior: 'bg-purple-100 text-purple-800',
  executive: 'bg-red-100 text-red-800',
};

const importanceColors = {
  low: 'bg-gray-100 text-gray-800',
  medium: 'bg-yellow-100 text-yellow-800',
  high: 'bg-orange-100 text-orange-800',
  critical: 'bg-red-100 text-red-800',
};

export function CareerRoleBrowser({ className }: CareerRoleBrowserProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRole, setSelectedRole] = useState<CareerRole | null>(null);
  const [filters, setFilters] = useState({
    level: '',
    industry: '',
    remote: false,
  });

  const filteredRoles = mockRoles.filter(role => {
    const matchesSearch = role.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         role.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesLevel = !filters.level || role.level === filters.level;
    const matchesIndustry = !filters.industry || role.industry === filters.industry;
    const matchesRemote = !filters.remote || role.remoteOptions;
    
    return matchesSearch && matchesLevel && matchesIndustry && matchesRemote;
  });

  const formatSalary = (role: CareerRole) => {
    const { min, max, currency, period } = role.salaryRange;
    const formatNumber = (num: number) => new Intl.NumberFormat().format(num);
    return `$${formatNumber(min)} - $${formatNumber(max)} ${period}`;
  };

  const getScoreColor = (score: number) => {
    if (score >= 8) return 'text-green-600';
    if (score >= 6) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={className}
    >
      <div className="space-y-6">
        {/* Search and Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Briefcase className="h-5 w-5" />
              Explore Career Roles
            </CardTitle>
            <CardDescription>
              Discover roles that match your interests and skills
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search roles..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                Filters
              </Button>
            </div>

            <div className="flex gap-4 text-sm">
              <select
                value={filters.level}
                onChange={(e) => setFilters({ ...filters, level: e.target.value })}
                className="px-3 py-1 border rounded"
              >
                <option value="">All Levels</option>
                <option value="entry">Entry Level</option>
                <option value="mid">Mid Level</option>
                <option value="senior">Senior Level</option>
                <option value="executive">Executive</option>
              </select>

              <select
                value={filters.industry}
                onChange={(e) => setFilters({ ...filters, industry: e.target.value })}
                className="px-3 py-1 border rounded"
              >
                <option value="">All Industries</option>
                <option value="Technology">Technology</option>
                <option value="Design">Design</option>
                <option value="Marketing">Marketing</option>
                <option value="Finance">Finance</option>
              </select>

              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={filters.remote}
                  onChange={(e) => setFilters({ ...filters, remote: e.target.checked })}
                />
                Remote Only
              </label>
            </div>
          </CardContent>
        </Card>

        {/* Role Cards Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredRoles.map((role) => (
            <motion.div
              key={role.id}
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            >
              <Card 
                className="cursor-pointer hover:shadow-lg transition-all duration-200 h-full"
                onClick={() => setSelectedRole(role)}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div>
                      <CardTitle className="text-lg">{role.title}</CardTitle>
                      <CardDescription className="text-sm mt-1">
                        {role.industry}
                      </CardDescription>
                    </div>
                    <div className="flex gap-1">
                      <Badge variant="outline" className={levelColors[role.level]}>
                        {role.level}
                      </Badge>
                      {role.remoteOptions && (
                        <Badge variant="outline" className="bg-blue-50 text-blue-700">
                          <Home className="h-3 w-3 mr-1" />
                          Remote
                        </Badge>
                      )}
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="space-y-4">
                  <p className="text-sm text-muted-foreground line-clamp-2">
                    {role.description}
                  </p>

                  {/* Salary */}
                  <div className="flex items-center gap-2 text-sm">
                    <DollarSign className="h-4 w-4 text-green-600" />
                    <span className="font-medium">{formatSalary(role)}</span>
                  </div>

                  {/* Location */}
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <MapPin className="h-4 w-4" />
                    <span>{role.salaryRange.location}</span>
                  </div>

                  {/* Scores */}
                  <div className="grid grid-cols-3 gap-2 text-xs">
                    <div className="text-center">
                      <div className={`font-bold ${getScoreColor(role.demandScore)}`}>
                        {role.demandScore}/10
                      </div>
                      <div className="text-muted-foreground">Demand</div>
                    </div>
                    <div className="text-center">
                      <div className={`font-bold ${getScoreColor(role.satisfactionScore)}`}>
                        {role.satisfactionScore}/10
                      </div>
                      <div className="text-muted-foreground">Satisfaction</div>
                    </div>
                    <div className="text-center">
                      <div className={`font-bold ${getScoreColor(role.workLifeBalance)}`}>
                        {role.workLifeBalance}/10
                      </div>
                      <div className="text-muted-foreground">Work-Life</div>
                    </div>
                  </div>

                  {/* Top Skills */}
                  <div className="space-y-2">
                    <div className="text-sm font-medium">Key Skills</div>
                    <div className="flex flex-wrap gap-1">
                      {role.skills.slice(0, 3).map((skill) => (
                        <Badge 
                          key={skill.id} 
                          variant="outline" 
                          className={`text-xs ${importanceColors[skill.importance]}`}
                        >
                          {skill.name}
                        </Badge>
                      ))}
                      {role.skills.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{role.skills.length - 3} more
                        </Badge>
                      )}
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-2 pt-2">
                    <Button size="sm" className="flex-1">
                      View Details
                    </Button>
                    <Button variant="outline" size="sm">
                      <Heart className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {filteredRoles.length === 0 && (
          <Card>
            <CardContent className="text-center py-12">
              <Search className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <h3 className="font-medium mb-2">No roles found</h3>
              <p className="text-sm text-muted-foreground">
                Try adjusting your search terms or filters
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </motion.div>
  );
}
