{"version": 3, "sources": ["../../src/lib/semver-noop.ts"], "sourcesContent": ["// DO NOT MODIFY THIS FILE DIRECTLY\n// It's for aliasing the `semver` package to be a noop for the `jsonwebtoken` package.\n// We're trying to minimize the size of the worker bundle.\n\nexport function satisfies() {\n  return true\n}\n"], "names": ["satisfies"], "mappings": "AAAA,mCAAmC;AACnC,sFAAsF;AACtF,0DAA0D;AAE1D,OAAO,SAASA;IACd,OAAO;AACT"}