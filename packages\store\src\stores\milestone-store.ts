import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import type { Milestone, MilestoneStatus, Task, TaskStatus } from '@kaizen/types';

interface MilestoneState {
  milestones: Milestone[];
  currentMilestone: Milestone | null;
  activeTasks: Task[];
  isLoading: boolean;
  error: string | null;
}

interface MilestoneActions {
  fetchMilestones: () => Promise<void>;
  setCurrentMilestone: (milestoneId: string) => void;
  updateMilestoneStatus: (milestoneId: string, status: MilestoneStatus) => Promise<void>;
  updateTaskStatus: (taskId: string, status: TaskStatus) => Promise<void>;
  submitTask: (taskId: string, submission: any) => Promise<void>;
  unlockMilestone: (milestoneId: string) => Promise<void>;
  clearError: () => void;
}

type MilestoneStore = MilestoneState & MilestoneActions;

export const useMilestoneStore = create<MilestoneStore>()(
  immer((set, get) => ({
    // Initial state
    milestones: [],
    currentMilestone: null,
    activeTasks: [],
    isLoading: false,
    error: null,

    // Actions
    fetchMilestones: async () => {
      set((state) => {
        state.isLoading = true;
        state.error = null;
      });

      try {
        // Mock API call - replace with actual API
        const response = await fetch('/api/milestones');
        if (!response.ok) {
          throw new Error('Failed to fetch milestones');
        }

        const milestones = await response.json();

        set((state) => {
          state.milestones = milestones;
          state.isLoading = false;
        });
      } catch (error) {
        set((state) => {
          state.error = error instanceof Error ? error.message : 'Failed to fetch milestones';
          state.isLoading = false;
        });
      }
    },

    setCurrentMilestone: (milestoneId: string) => {
      const milestone = get().milestones.find(m => m.id === milestoneId);
      if (milestone) {
        set((state) => {
          state.currentMilestone = milestone;
          state.activeTasks = milestone.tasks.filter(task => 
            task.status === 'not_started' || task.status === 'in_progress'
          );
        });
      }
    },

    updateMilestoneStatus: async (milestoneId: string, status: MilestoneStatus) => {
      try {
        // Mock API call - replace with actual API
        const response = await fetch(`/api/milestones/${milestoneId}/status`, {
          method: 'PATCH',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ status }),
        });

        if (!response.ok) {
          throw new Error('Failed to update milestone status');
        }

        set((state) => {
          const milestone = state.milestones.find(m => m.id === milestoneId);
          if (milestone) {
            milestone.status = status;
          }
          if (state.currentMilestone?.id === milestoneId) {
            state.currentMilestone.status = status;
          }
        });
      } catch (error) {
        set((state) => {
          state.error = error instanceof Error ? error.message : 'Failed to update milestone status';
        });
      }
    },

    updateTaskStatus: async (taskId: string, status: TaskStatus) => {
      try {
        // Mock API call - replace with actual API
        const response = await fetch(`/api/tasks/${taskId}/status`, {
          method: 'PATCH',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ status }),
        });

        if (!response.ok) {
          throw new Error('Failed to update task status');
        }

        set((state) => {
          // Update task in milestones
          state.milestones.forEach(milestone => {
            const task = milestone.tasks.find(t => t.id === taskId);
            if (task) {
              task.status = status;
            }
          });

          // Update task in current milestone
          if (state.currentMilestone) {
            const task = state.currentMilestone.tasks.find(t => t.id === taskId);
            if (task) {
              task.status = status;
            }
          }

          // Update active tasks
          state.activeTasks = state.activeTasks.map(task =>
            task.id === taskId ? { ...task, status } : task
          );
        });
      } catch (error) {
        set((state) => {
          state.error = error instanceof Error ? error.message : 'Failed to update task status';
        });
      }
    },

    submitTask: async (taskId: string, submission: any) => {
      try {
        // Mock API call - replace with actual API
        const response = await fetch(`/api/tasks/${taskId}/submit`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(submission),
        });

        if (!response.ok) {
          throw new Error('Failed to submit task');
        }

        // Update task status to submitted
        await get().updateTaskStatus(taskId, 'submitted');
      } catch (error) {
        set((state) => {
          state.error = error instanceof Error ? error.message : 'Failed to submit task';
        });
      }
    },

    unlockMilestone: async (milestoneId: string) => {
      try {
        // Mock API call - replace with actual API
        const response = await fetch(`/api/milestones/${milestoneId}/unlock`, {
          method: 'POST',
        });

        if (!response.ok) {
          throw new Error('Failed to unlock milestone');
        }

        set((state) => {
          const milestone = state.milestones.find(m => m.id === milestoneId);
          if (milestone && milestone.status === 'locked') {
            milestone.status = 'unlocked';
          }
        });
      } catch (error) {
        set((state) => {
          state.error = error instanceof Error ? error.message : 'Failed to unlock milestone';
        });
      }
    },

    clearError: () => {
      set((state) => {
        state.error = null;
      });
    },
  }))
);
