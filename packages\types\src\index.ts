// User and Authentication Types
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  createdAt: Date;
  updatedAt: Date;
  profile: UserProfile;
}

export interface UserProfile {
  id: string;
  userId: string;
  currentXP: number;
  totalXP: number;
  level: number;
  streak: number;
  lastActiveDate: Date;
  selectedCareerPath?: string;
  completedMilestones: string[];
  achievements: Achievement[];
  preferences: UserPreferences;
}

export interface UserPreferences {
  language: string;
  timezone: string;
  notifications: NotificationSettings;
  theme: 'light' | 'dark' | 'system';
}

export interface NotificationSettings {
  email: boolean;
  push: boolean;
  milestoneReminders: boolean;
  weeklyProgress: boolean;
  achievements: boolean;
}

// Milestone and Journey Types
export interface Milestone {
  id: string;
  title: string;
  description: string;
  order: number;
  status: MilestoneStatus;
  xpReward: number;
  estimatedDuration: number; // in minutes
  prerequisites: string[];
  tasks: Task[];
  unlockConditions: UnlockCondition[];
  category: MilestoneCategory;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
}

export type MilestoneStatus = 'locked' | 'unlocked' | 'active' | 'completed';

export type MilestoneCategory = 
  | 'self-discovery' 
  | 'career-exploration' 
  | 'skill-building' 
  | 'networking' 
  | 'job-search' 
  | 'interview-prep';

export interface UnlockCondition {
  type: 'milestone_completion' | 'xp_threshold' | 'time_based' | 'task_completion';
  value: string | number;
  description: string;
}

// Task Types
export interface Task {
  id: string;
  milestoneId: string;
  title: string;
  description: string;
  type: TaskType;
  order: number;
  status: TaskStatus;
  xpReward: number;
  estimatedDuration: number;
  content: TaskContent;
  submissions?: TaskSubmission[];
  feedback?: TaskFeedback[];
  createdAt: Date;
  updatedAt: Date;
}

export type TaskType = 
  | 'quiz' 
  | 'reflection' 
  | 'upload' 
  | 'peer-review' 
  | 'chat' 
  | 'form' 
  | 'research';

export type TaskStatus = 'not_started' | 'in_progress' | 'submitted' | 'reviewed' | 'completed';

export interface TaskContent {
  instructions: string;
  questions?: QuizQuestion[] | ReflectionPrompt[];
  uploadRequirements?: UploadRequirements;
  formFields?: FormField[];
  chatPrompts?: ChatPrompt[];
}

export interface QuizQuestion {
  id: string;
  question: string;
  type: 'multiple_choice' | 'single_choice' | 'true_false' | 'text';
  options?: string[];
  correctAnswer?: string | string[];
  explanation?: string;
  points: number;
}

export interface ReflectionPrompt {
  id: string;
  prompt: string;
  minWords?: number;
  maxWords?: number;
  guidelines: string[];
}

export interface UploadRequirements {
  allowedTypes: string[];
  maxSize: number; // in MB
  maxFiles: number;
  description: string;
  examples?: string[];
}

export interface FormField {
  id: string;
  label: string;
  type: 'text' | 'textarea' | 'select' | 'multiselect' | 'date' | 'number';
  required: boolean;
  options?: string[];
  validation?: FieldValidation;
  placeholder?: string;
  helperText?: string;
}

export interface FieldValidation {
  min?: number;
  max?: number;
  pattern?: string;
  message?: string;
}

export interface ChatPrompt {
  id: string;
  agentType: AgentType;
  initialMessage: string;
  context: string;
  objectives: string[];
}

// Agent and Chat Types
export type AgentType = 'coach' | 'designer' | 'mentor' | 'buddy';

export interface Agent {
  id: string;
  type: AgentType;
  name: string;
  avatar: string;
  description: string;
  personality: string[];
  specialties: string[];
  isActive: boolean;
}

export interface ChatMessage {
  id: string;
  chatId: string;
  agentId?: string;
  userId?: string;
  content: string;
  type: 'text' | 'action' | 'system';
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface ChatSession {
  id: string;
  userId: string;
  agentId: string;
  taskId?: string;
  milestoneId?: string;
  status: 'active' | 'completed' | 'archived';
  messages: ChatMessage[];
  context: ChatContext;
  createdAt: Date;
  updatedAt: Date;
}

export interface ChatContext {
  currentMilestone?: string;
  currentTask?: string;
  userGoals: string[];
  conversationObjectives: string[];
  previousSessions: string[];
}

// Career and Role Types
export interface CareerRole {
  id: string;
  title: string;
  description: string;
  industry: string;
  level: 'entry' | 'mid' | 'senior' | 'executive';
  salaryRange: SalaryRange;
  skills: Skill[];
  responsibilities: string[];
  qualifications: string[];
  growthPath: string[];
  demandScore: number; // 1-10
  satisfactionScore: number; // 1-10
  workLifeBalance: number; // 1-10
  remoteOptions: boolean;
  tags: string[];
  relatedRoles: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface SalaryRange {
  min: number;
  max: number;
  currency: string;
  period: 'hourly' | 'monthly' | 'yearly';
  location: string;
}

export interface Skill {
  id: string;
  name: string;
  category: SkillCategory;
  level: SkillLevel;
  importance: 'low' | 'medium' | 'high' | 'critical';
  description?: string;
  resources?: LearningResource[];
}

export type SkillCategory = 
  | 'technical' 
  | 'soft' 
  | 'leadership' 
  | 'communication' 
  | 'analytical' 
  | 'creative';

export type SkillLevel = 'beginner' | 'intermediate' | 'advanced' | 'expert';

export interface LearningResource {
  id: string;
  title: string;
  type: 'course' | 'book' | 'article' | 'video' | 'certification';
  url?: string;
  provider: string;
  duration?: number;
  cost?: number;
  rating?: number;
  description: string;
}

// Roadmap and Planning Types
export interface LearningRoadmap {
  id: string;
  userId: string;
  careerRoleId: string;
  title: string;
  description: string;
  status: 'draft' | 'active' | 'completed' | 'paused';
  estimatedDuration: number; // in weeks
  milestones: RoadmapMilestone[];
  customizations: RoadmapCustomization[];
  progress: RoadmapProgress;
  createdAt: Date;
  updatedAt: Date;
}

export interface RoadmapMilestone {
  id: string;
  milestoneId: string;
  order: number;
  estimatedStartDate?: Date;
  estimatedEndDate?: Date;
  actualStartDate?: Date;
  actualEndDate?: Date;
  dependencies: string[];
  customizations?: MilestoneCustomization;
}

export interface MilestoneCustomization {
  title?: string;
  description?: string;
  additionalTasks?: Task[];
  removedTasks?: string[];
  priorityLevel: 'low' | 'medium' | 'high';
}

export interface RoadmapCustomization {
  id: string;
  type: 'reorder' | 'add_milestone' | 'remove_milestone' | 'modify_milestone';
  description: string;
  data: Record<string, any>;
  appliedAt: Date;
}

export interface RoadmapProgress {
  completedMilestones: number;
  totalMilestones: number;
  completedTasks: number;
  totalTasks: number;
  earnedXP: number;
  totalXP: number;
  estimatedCompletion: Date;
  actualPace: 'ahead' | 'on_track' | 'behind';
}

// Portfolio and Submission Types
export interface Portfolio {
  id: string;
  userId: string;
  title: string;
  description: string;
  visibility: 'private' | 'public' | 'mentors_only';
  projects: PortfolioProject[];
  skills: Skill[];
  achievements: Achievement[];
  testimonials: Testimonial[];
  createdAt: Date;
  updatedAt: Date;
}

export interface PortfolioProject {
  id: string;
  portfolioId: string;
  title: string;
  description: string;
  type: 'work_sample' | 'case_study' | 'reflection' | 'certification';
  files: ProjectFile[];
  skills: string[];
  tools: string[];
  status: 'draft' | 'submitted' | 'approved' | 'needs_revision';
  feedback: ProjectFeedback[];
  metadata: ProjectMetadata;
  createdAt: Date;
  updatedAt: Date;
}

export interface ProjectFile {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  thumbnail?: string;
  uploadedAt: Date;
}

export interface ProjectFeedback {
  id: string;
  projectId: string;
  reviewerId: string;
  reviewerType: 'mentor' | 'peer' | 'system';
  rating?: number; // 1-5
  comments: string;
  suggestions: string[];
  approved: boolean;
  createdAt: Date;
}

export interface ProjectMetadata {
  taskId?: string;
  milestoneId?: string;
  submissionDate?: Date;
  reviewDeadline?: Date;
  tags: string[];
  category: string;
}

// Achievement and Gamification Types
export interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  category: AchievementCategory;
  rarity: 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary';
  xpReward: number;
  conditions: AchievementCondition[];
  unlockedAt?: Date;
  progress?: AchievementProgress;
}

export type AchievementCategory = 
  | 'milestone' 
  | 'streak' 
  | 'social' 
  | 'skill' 
  | 'speed' 
  | 'exploration' 
  | 'special';

export interface AchievementCondition {
  type: 'milestone_count' | 'streak_days' | 'xp_total' | 'task_type' | 'time_based';
  value: number | string;
  operator: 'equals' | 'greater_than' | 'less_than' | 'contains';
}

export interface AchievementProgress {
  current: number;
  target: number;
  percentage: number;
}

// Submission and Feedback Types
export interface TaskSubmission {
  id: string;
  taskId: string;
  userId: string;
  content: SubmissionContent;
  status: 'submitted' | 'under_review' | 'approved' | 'needs_revision';
  submittedAt: Date;
  reviewedAt?: Date;
  score?: number;
  feedback?: TaskFeedback[];
}

export interface SubmissionContent {
  answers?: Record<string, any>;
  files?: ProjectFile[];
  text?: string;
  metadata?: Record<string, any>;
}

export interface TaskFeedback {
  id: string;
  submissionId: string;
  reviewerId: string;
  reviewerType: 'mentor' | 'peer' | 'ai' | 'system';
  type: 'general' | 'specific' | 'suggestion' | 'correction';
  content: string;
  rating?: number;
  helpful?: boolean;
  createdAt: Date;
}

export interface Testimonial {
  id: string;
  portfolioId: string;
  authorId: string;
  authorName: string;
  authorRole: string;
  content: string;
  rating: number;
  verified: boolean;
  createdAt: Date;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: ApiError;
  message?: string;
  meta?: ResponseMeta;
}

export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, any>;
  field?: string;
}

export interface ResponseMeta {
  page?: number;
  limit?: number;
  total?: number;
  hasMore?: boolean;
}

// Filter and Search Types
export interface FilterOptions {
  categories?: string[];
  difficulty?: string[];
  duration?: [number, number];
  tags?: string[];
  status?: string[];
  dateRange?: [Date, Date];
}

export interface SearchOptions {
  query?: string;
  filters?: FilterOptions;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface SortOption {
  value: string;
  label: string;
  direction: 'asc' | 'desc';
}
