{"name": "@kaizen/ui", "version": "1.0.0", "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": "./src/index.ts"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint src/**/*.{ts,tsx}", "type-check": "tsc --noEmit", "test": "jest", "test:coverage": "jest --coverage", "clean": "rm -rf dist coverage", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"react": "^18.3.0", "react-dom": "^18.3.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-badge": "^1.0.0", "@radix-ui/react-button": "^1.0.0", "@radix-ui/react-card": "^1.0.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "lucide-react": "^0.290.0", "framer-motion": "^10.16.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "typescript": "^5.2.0", "@kaizen/eslint-config": "workspace:*", "eslint": "^8.48.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "@testing-library/react": "^14.0.0", "@testing-library/jest-dom": "^6.1.0", "@storybook/react": "^7.5.0", "@storybook/react-vite": "^7.5.0", "@storybook/addon-essentials": "^7.5.0", "@storybook/addon-interactions": "^7.5.0", "@storybook/addon-links": "^7.5.0", "@storybook/blocks": "^7.5.0", "@storybook/testing-library": "^0.2.0"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}}