{"version": 3, "sources": ["../../../../src/server/web/spec-extension/url-pattern.ts"], "sourcesContent": ["const GlobalURLPattern =\n  // @ts-expect-error: URLPattern is not available in Node.js\n  typeof URLPattern === 'undefined' ? undefined : URLPattern\n\nexport { GlobalURLPattern as URLPattern }\n"], "names": ["URLPattern", "GlobalURLPattern", "undefined"], "mappings": ";;;;+BAI6BA;;;eAApBC;;;AAJT,MAAMA,mBACJ,2DAA2D;AAC3D,OAAOD,eAAe,cAAcE,YAAYF"}