{"name": "@kaizen/store", "version": "1.0.0", "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": "./src/index.ts"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint src/**/*.ts", "type-check": "tsc --noEmit", "test": "jest", "clean": "rm -rf dist coverage"}, "dependencies": {"zustand": "^4.4.0", "immer": "^10.0.0", "@kaizen/types": "workspace:*"}, "devDependencies": {"@types/node": "^20.8.0", "typescript": "^5.2.0", "@kaizen/eslint-config": "workspace:*", "eslint": "^8.48.0", "jest": "^29.7.0"}}