{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "pipeline": {"build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**", "dist/**"]}, "dev": {"cache": false, "persistent": true}, "lint": {"dependsOn": ["^lint"]}, "test": {"dependsOn": ["^build"], "outputs": ["coverage/**"]}, "test:coverage": {"dependsOn": ["^build"], "outputs": ["coverage/**"]}, "type-check": {"dependsOn": ["^build"]}, "clean": {"cache": false}, "storybook": {"cache": false, "persistent": true}, "build-storybook": {"dependsOn": ["^build"], "outputs": ["storybook-static/**"]}}}