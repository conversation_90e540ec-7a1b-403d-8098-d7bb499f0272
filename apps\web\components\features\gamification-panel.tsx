'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@workspace/ui/components/card';
import { Badge } from '@workspace/ui/components/badge';
import { Progress } from '@workspace/ui/components/progress';
import { But<PERSON> } from '@workspace/ui/components/button';
import { 
  Trophy, 
  Star, 
  Flame, 
  Target, 
  Award, 
  TrendingUp,
  Calendar,
  Zap
} from 'lucide-react';
import { Achievement, UserProfile } from '../../types';
import { useAuth } from '../../lib/contexts/auth-context';

interface GamificationPanelProps {
  className?: string;
  compact?: boolean;
}

// Mock data - replace with real data from context/API
const mockAchievements: Achievement[] = [
  {
    id: '1',
    title: 'First Steps',
    description: 'Complete your first milestone',
    icon: '🎯',
    category: 'milestone',
    rarity: 'common',
    xpReward: 100,
    conditions: [],
    unlockedAt: new Date(),
  },
  {
    id: '2',
    title: 'Streak Master',
    description: 'Maintain a 7-day learning streak',
    icon: '🔥',
    category: 'streak',
    rarity: 'uncommon',
    xpReward: 250,
    conditions: [],
    progress: { current: 5, target: 7, percentage: 71 },
  },
  {
    id: '3',
    title: 'Knowledge Seeker',
    description: 'Complete 10 reflection tasks',
    icon: '📚',
    category: 'skill',
    rarity: 'rare',
    xpReward: 500,
    conditions: [],
    progress: { current: 7, target: 10, percentage: 70 },
  },
];

const rarityConfig = {
  common: { color: 'bg-gray-100 text-gray-800', glow: 'shadow-gray-200' },
  uncommon: { color: 'bg-green-100 text-green-800', glow: 'shadow-green-200' },
  rare: { color: 'bg-blue-100 text-blue-800', glow: 'shadow-blue-200' },
  epic: { color: 'bg-purple-100 text-purple-800', glow: 'shadow-purple-200' },
  legendary: { color: 'bg-yellow-100 text-yellow-800', glow: 'shadow-yellow-200' },
};

export function GamificationPanel({ className, compact = false }: GamificationPanelProps) {
  const { user } = useAuth();
  
  // Mock profile data - replace with real data
  const profile: UserProfile = user?.profile || {
    id: '1',
    userId: '1',
    currentXP: 1250,
    totalXP: 1250,
    level: 3,
    streak: 5,
    lastActiveDate: new Date(),
    selectedCareerPath: 'Software Development',
    completedMilestones: ['1', '2'],
    achievements: mockAchievements.filter(a => a.unlockedAt),
  };

  const xpToNextLevel = 500; // Calculate based on level
  const xpProgress = (profile.currentXP % 500) / 500 * 100; // Simplified calculation

  const unlockedAchievements = mockAchievements.filter(a => a.unlockedAt);
  const inProgressAchievements = mockAchievements.filter(a => a.progress && !a.unlockedAt);

  if (compact) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className={className}
      >
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="bg-blue-100 p-2 rounded-full">
                  <Trophy className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <div className="flex items-center gap-2">
                    <span className="font-bold text-lg">Level {profile.level}</span>
                    <Badge variant="outline" className="text-xs">
                      {profile.currentXP} XP
                    </Badge>
                  </div>
                  <Progress value={xpProgress} className="w-24 h-2 mt-1" variant="xp" />
                </div>
              </div>
              
              <div className="flex items-center gap-4 text-sm">
                <div className="flex items-center gap-1">
                  <Flame className="h-4 w-4 text-orange-500" />
                  <span className="font-medium">{profile.streak}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Award className="h-4 w-4 text-yellow-500" />
                  <span className="font-medium">{unlockedAchievements.length}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={className}
    >
      <div className="space-y-6">
        {/* Level & XP Card */}
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Trophy className="h-5 w-5 text-blue-600" />
              Level Progress
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-3xl font-bold text-blue-600">Level {profile.level}</div>
                <div className="text-sm text-muted-foreground">
                  {profile.currentXP} / {profile.currentXP + xpToNextLevel} XP
                </div>
              </div>
              <div className="text-right">
                <div className="text-lg font-semibold">{xpToNextLevel} XP</div>
                <div className="text-sm text-muted-foreground">to next level</div>
              </div>
            </div>
            <Progress value={xpProgress} className="h-3" variant="xp" />
          </CardContent>
        </Card>

        {/* Stats Grid */}
        <div className="grid grid-cols-2 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="bg-orange-100 p-2 rounded-full">
                  <Flame className="h-5 w-5 text-orange-600" />
                </div>
                <div>
                  <div className="text-2xl font-bold">{profile.streak}</div>
                  <div className="text-sm text-muted-foreground">Day Streak</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="bg-green-100 p-2 rounded-full">
                  <Target className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <div className="text-2xl font-bold">{profile.completedMilestones.length}</div>
                  <div className="text-sm text-muted-foreground">Milestones</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Achievements */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="h-5 w-5 text-yellow-600" />
              Recent Achievements
            </CardTitle>
            <CardDescription>
              Your latest accomplishments
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {unlockedAchievements.slice(0, 3).map((achievement) => (
                <motion.div
                  key={achievement.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  className={`flex items-center gap-3 p-3 rounded-lg border ${rarityConfig[achievement.rarity].glow} shadow-sm`}
                >
                  <div className="text-2xl">{achievement.icon}</div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{achievement.title}</span>
                      <Badge 
                        variant="outline" 
                        className={`text-xs ${rarityConfig[achievement.rarity].color}`}
                      >
                        {achievement.rarity}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">{achievement.description}</p>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-yellow-600">
                      +{achievement.xpReward} XP
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Progress Achievements */}
        {inProgressAchievements.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-blue-600" />
                In Progress
              </CardTitle>
              <CardDescription>
                Achievements you're working towards
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {inProgressAchievements.map((achievement) => (
                  <div key={achievement.id} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="text-lg">{achievement.icon}</span>
                        <div>
                          <span className="font-medium">{achievement.title}</span>
                          <p className="text-sm text-muted-foreground">{achievement.description}</p>
                        </div>
                      </div>
                      <Badge 
                        variant="outline" 
                        className={`text-xs ${rarityConfig[achievement.rarity].color}`}
                      >
                        {achievement.rarity}
                      </Badge>
                    </div>
                    {achievement.progress && (
                      <div className="space-y-1">
                        <div className="flex justify-between text-sm">
                          <span>Progress</span>
                          <span>{achievement.progress.current}/{achievement.progress.target}</span>
                        </div>
                        <Progress value={achievement.progress.percentage} className="h-2" />
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-purple-600" />
              Quick Actions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-3">
              <Button variant="outline" size="sm" className="justify-start">
                <Calendar className="h-4 w-4 mr-2" />
                View All Achievements
              </Button>
              <Button variant="outline" size="sm" className="justify-start">
                <Star className="h-4 w-4 mr-2" />
                Leaderboard
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </motion.div>
  );
}
