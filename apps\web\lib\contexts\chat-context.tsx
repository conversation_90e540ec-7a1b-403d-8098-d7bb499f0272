'use client';

import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { ChatSession, ChatMessage, Agent, AgentType } from '../../types';

interface ChatState {
  sessions: ChatSession[];
  activeSession: ChatSession | null;
  agents: Agent[];
  isLoading: boolean;
  isTyping: boolean;
  error: string | null;
}

type ChatAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_AGENTS'; payload: Agent[] }
  | { type: 'SET_SESSIONS'; payload: ChatSession[] }
  | { type: 'ADD_SESSION'; payload: ChatSession }
  | { type: 'SET_ACTIVE_SESSION'; payload: ChatSession | null }
  | { type: 'ADD_MESSAGE'; payload: { sessionId: string; message: ChatMessage } }
  | { type: 'SET_TYPING'; payload: boolean }
  | { type: 'UPDATE_SESSION_STATUS'; payload: { sessionId: string; status: 'active' | 'completed' | 'archived' } }
  | { type: 'SET_ERROR'; payload: string }
  | { type: 'CLEAR_ERROR' };

interface ChatContextType extends ChatState {
  fetchAgents: () => Promise<void>;
  createSession: (agentId: string, context?: any) => Promise<string>;
  setActiveSession: (sessionId: string) => void;
  sendMessage: (content: string, sessionId?: string) => Promise<void>;
  endSession: (sessionId: string) => Promise<void>;
  clearError: () => void;
}

const ChatContext = createContext<ChatContextType | undefined>(undefined);

const chatReducer = (state: ChatState, action: ChatAction): ChatState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'SET_AGENTS':
      return { ...state, agents: action.payload, isLoading: false };
    case 'SET_SESSIONS':
      return { ...state, sessions: action.payload };
    case 'ADD_SESSION':
      return { 
        ...state, 
        sessions: [...state.sessions, action.payload],
        activeSession: action.payload 
      };
    case 'SET_ACTIVE_SESSION':
      return { ...state, activeSession: action.payload };
    case 'ADD_MESSAGE':
      return {
        ...state,
        sessions: state.sessions.map(session =>
          session.id === action.payload.sessionId
            ? { ...session, messages: [...session.messages, action.payload.message] }
            : session
        ),
        activeSession: state.activeSession?.id === action.payload.sessionId
          ? { ...state.activeSession, messages: [...state.activeSession.messages, action.payload.message] }
          : state.activeSession,
      };
    case 'SET_TYPING':
      return { ...state, isTyping: action.payload };
    case 'UPDATE_SESSION_STATUS':
      return {
        ...state,
        sessions: state.sessions.map(session =>
          session.id === action.payload.sessionId
            ? { ...session, status: action.payload.status }
            : session
        ),
        activeSession: state.activeSession?.id === action.payload.sessionId
          ? { ...state.activeSession, status: action.payload.status }
          : state.activeSession,
      };
    case 'SET_ERROR':
      return { ...state, error: action.payload, isLoading: false };
    case 'CLEAR_ERROR':
      return { ...state, error: null };
    default:
      return state;
  }
};

const initialState: ChatState = {
  sessions: [],
  activeSession: null,
  agents: [],
  isLoading: false,
  isTyping: false,
  error: null,
};

export function ChatProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(chatReducer, initialState);

  const fetchAgents = async () => {
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'CLEAR_ERROR' });

    try {
      const response = await fetch('/api/agents');
      if (!response.ok) {
        throw new Error('Failed to fetch agents');
      }

      const agents = await response.json();
      dispatch({ type: 'SET_AGENTS', payload: agents });
    } catch (error) {
      dispatch({
        type: 'SET_ERROR',
        payload: error instanceof Error ? error.message : 'Failed to fetch agents',
      });
    }
  };

  const createSession = async (agentId: string, context?: any): Promise<string> => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/chat/sessions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(token && { Authorization: `Bearer ${token}` }),
        },
        body: JSON.stringify({ agentId, context }),
      });

      if (!response.ok) {
        throw new Error('Failed to create chat session');
      }

      const session = await response.json();
      dispatch({ type: 'ADD_SESSION', payload: session });
      return session.id;
    } catch (error) {
      dispatch({
        type: 'SET_ERROR',
        payload: error instanceof Error ? error.message : 'Failed to create chat session',
      });
      throw error;
    }
  };

  const setActiveSession = (sessionId: string) => {
    const session = state.sessions.find(s => s.id === sessionId);
    dispatch({ type: 'SET_ACTIVE_SESSION', payload: session || null });
  };

  const sendMessage = async (content: string, sessionId?: string) => {
    const targetSessionId = sessionId || state.activeSession?.id;
    if (!targetSessionId) {
      throw new Error('No active session');
    }

    const userMessage: ChatMessage = {
      id: `msg_${Date.now()}`,
      chatId: targetSessionId,
      userId: 'current_user', // This should come from auth context
      content,
      type: 'text',
      timestamp: new Date(),
    };

    // Add user message immediately
    dispatch({ 
      type: 'ADD_MESSAGE', 
      payload: { sessionId: targetSessionId, message: userMessage } 
    });

    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch(`/api/chat/sessions/${targetSessionId}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(token && { Authorization: `Bearer ${token}` }),
        },
        body: JSON.stringify({ content, type: 'text' }),
      });

      if (!response.ok) {
        throw new Error('Failed to send message');
      }

      // Set typing indicator
      dispatch({ type: 'SET_TYPING', payload: true });

      // Simulate agent response (replace with actual WebSocket or polling)
      setTimeout(() => {
        const agentMessage: ChatMessage = {
          id: `msg_${Date.now() + 1}`,
          chatId: targetSessionId,
          agentId: state.activeSession?.agentId,
          content: 'This is a mock response from the agent. In a real implementation, this would come from your AI service.',
          type: 'text',
          timestamp: new Date(),
        };

        dispatch({ 
          type: 'ADD_MESSAGE', 
          payload: { sessionId: targetSessionId, message: agentMessage } 
        });
        dispatch({ type: 'SET_TYPING', payload: false });
      }, 2000);

    } catch (error) {
      dispatch({
        type: 'SET_ERROR',
        payload: error instanceof Error ? error.message : 'Failed to send message',
      });
      dispatch({ type: 'SET_TYPING', payload: false });
    }
  };

  const endSession = async (sessionId: string) => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch(`/api/chat/sessions/${sessionId}/end`, {
        method: 'POST',
        headers: token ? { Authorization: `Bearer ${token}` } : {},
      });

      if (!response.ok) {
        throw new Error('Failed to end session');
      }

      dispatch({ 
        type: 'UPDATE_SESSION_STATUS', 
        payload: { sessionId, status: 'completed' } 
      });

      if (state.activeSession?.id === sessionId) {
        dispatch({ type: 'SET_ACTIVE_SESSION', payload: null });
      }
    } catch (error) {
      dispatch({
        type: 'SET_ERROR',
        payload: error instanceof Error ? error.message : 'Failed to end session',
      });
    }
  };

  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  // Auto-fetch agents on mount
  useEffect(() => {
    fetchAgents();
  }, []);

  const value: ChatContextType = {
    ...state,
    fetchAgents,
    createSession,
    setActiveSession,
    sendMessage,
    endSession,
    clearError,
  };

  return <ChatContext.Provider value={value}>{children}</ChatContext.Provider>;
}

export function useChat() {
  const context = useContext(ChatContext);
  if (context === undefined) {
    throw new Error('useChat must be used within a ChatProvider');
  }
  return context;
}
