'use client';

import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { Milestone, MilestoneStatus, Task, TaskStatus } from '../../types';

interface MilestoneState {
  milestones: Milestone[];
  currentMilestone: Milestone | null;
  activeTasks: Task[];
  isLoading: boolean;
  error: string | null;
}

type MilestoneAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_MILESTONES'; payload: Milestone[] }
  | { type: 'SET_CURRENT_MILESTONE'; payload: Milestone | null }
  | { type: 'UPDATE_MILESTONE_STATUS'; payload: { id: string; status: MilestoneStatus } }
  | { type: 'UPDATE_TASK_STATUS'; payload: { id: string; status: TaskStatus } }
  | { type: 'SET_ACTIVE_TASKS'; payload: Task[] }
  | { type: 'SET_ERROR'; payload: string }
  | { type: 'CLEAR_ERROR' };

interface MilestoneContextType extends MilestoneState {
  fetchMilestones: () => Promise<void>;
  setCurrentMilestone: (milestoneId: string) => void;
  updateMilestoneStatus: (milestoneId: string, status: MilestoneStatus) => Promise<void>;
  updateTaskStatus: (taskId: string, status: TaskStatus) => Promise<void>;
  submitTask: (taskId: string, submission: any) => Promise<void>;
  unlockMilestone: (milestoneId: string) => Promise<void>;
  clearError: () => void;
}

const MilestoneContext = createContext<MilestoneContextType | undefined>(undefined);

const milestoneReducer = (state: MilestoneState, action: MilestoneAction): MilestoneState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'SET_MILESTONES':
      return { ...state, milestones: action.payload, isLoading: false };
    case 'SET_CURRENT_MILESTONE':
      return { 
        ...state, 
        currentMilestone: action.payload,
        activeTasks: action.payload?.tasks.filter(task => 
          task.status === 'not_started' || task.status === 'in_progress'
        ) || []
      };
    case 'UPDATE_MILESTONE_STATUS':
      return {
        ...state,
        milestones: state.milestones.map(milestone =>
          milestone.id === action.payload.id
            ? { ...milestone, status: action.payload.status }
            : milestone
        ),
        currentMilestone: state.currentMilestone?.id === action.payload.id
          ? { ...state.currentMilestone, status: action.payload.status }
          : state.currentMilestone,
      };
    case 'UPDATE_TASK_STATUS':
      return {
        ...state,
        milestones: state.milestones.map(milestone => ({
          ...milestone,
          tasks: milestone.tasks.map(task =>
            task.id === action.payload.id
              ? { ...task, status: action.payload.status }
              : task
          ),
        })),
        currentMilestone: state.currentMilestone ? {
          ...state.currentMilestone,
          tasks: state.currentMilestone.tasks.map(task =>
            task.id === action.payload.id
              ? { ...task, status: action.payload.status }
              : task
          ),
        } : null,
        activeTasks: state.activeTasks.map(task =>
          task.id === action.payload.id
            ? { ...task, status: action.payload.status }
            : task
        ),
      };
    case 'SET_ACTIVE_TASKS':
      return { ...state, activeTasks: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload, isLoading: false };
    case 'CLEAR_ERROR':
      return { ...state, error: null };
    default:
      return state;
  }
};

const initialState: MilestoneState = {
  milestones: [],
  currentMilestone: null,
  activeTasks: [],
  isLoading: false,
  error: null,
};

export function MilestoneProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(milestoneReducer, initialState);

  const fetchMilestones = async () => {
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'CLEAR_ERROR' });

    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/milestones', {
        headers: token ? { Authorization: `Bearer ${token}` } : {},
      });

      if (!response.ok) {
        throw new Error('Failed to fetch milestones');
      }

      const milestones = await response.json();
      dispatch({ type: 'SET_MILESTONES', payload: milestones });
    } catch (error) {
      dispatch({
        type: 'SET_ERROR',
        payload: error instanceof Error ? error.message : 'Failed to fetch milestones',
      });
    }
  };

  const setCurrentMilestone = (milestoneId: string) => {
    const milestone = state.milestones.find(m => m.id === milestoneId);
    dispatch({ type: 'SET_CURRENT_MILESTONE', payload: milestone || null });
  };

  const updateMilestoneStatus = async (milestoneId: string, status: MilestoneStatus) => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch(`/api/milestones/${milestoneId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          ...(token && { Authorization: `Bearer ${token}` }),
        },
        body: JSON.stringify({ status }),
      });

      if (!response.ok) {
        throw new Error('Failed to update milestone status');
      }

      dispatch({ type: 'UPDATE_MILESTONE_STATUS', payload: { id: milestoneId, status } });
    } catch (error) {
      dispatch({
        type: 'SET_ERROR',
        payload: error instanceof Error ? error.message : 'Failed to update milestone status',
      });
    }
  };

  const updateTaskStatus = async (taskId: string, status: TaskStatus) => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch(`/api/tasks/${taskId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          ...(token && { Authorization: `Bearer ${token}` }),
        },
        body: JSON.stringify({ status }),
      });

      if (!response.ok) {
        throw new Error('Failed to update task status');
      }

      dispatch({ type: 'UPDATE_TASK_STATUS', payload: { id: taskId, status } });
    } catch (error) {
      dispatch({
        type: 'SET_ERROR',
        payload: error instanceof Error ? error.message : 'Failed to update task status',
      });
    }
  };

  const submitTask = async (taskId: string, submission: any) => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch(`/api/tasks/${taskId}/submit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(token && { Authorization: `Bearer ${token}` }),
        },
        body: JSON.stringify(submission),
      });

      if (!response.ok) {
        throw new Error('Failed to submit task');
      }

      // Update task status to submitted
      dispatch({ type: 'UPDATE_TASK_STATUS', payload: { id: taskId, status: 'submitted' } });
    } catch (error) {
      dispatch({
        type: 'SET_ERROR',
        payload: error instanceof Error ? error.message : 'Failed to submit task',
      });
    }
  };

  const unlockMilestone = async (milestoneId: string) => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch(`/api/milestones/${milestoneId}/unlock`, {
        method: 'POST',
        headers: token ? { Authorization: `Bearer ${token}` } : {},
      });

      if (!response.ok) {
        throw new Error('Failed to unlock milestone');
      }

      dispatch({ type: 'UPDATE_MILESTONE_STATUS', payload: { id: milestoneId, status: 'unlocked' } });
    } catch (error) {
      dispatch({
        type: 'SET_ERROR',
        payload: error instanceof Error ? error.message : 'Failed to unlock milestone',
      });
    }
  };

  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  // Auto-fetch milestones on mount
  useEffect(() => {
    fetchMilestones();
  }, []);

  const value: MilestoneContextType = {
    ...state,
    fetchMilestones,
    setCurrentMilestone,
    updateMilestoneStatus,
    updateTaskStatus,
    submitTask,
    unlockMilestone,
    clearError,
  };

  return (
    <MilestoneContext.Provider value={value}>
      {children}
    </MilestoneContext.Provider>
  );
}

export function useMilestone() {
  const context = useContext(MilestoneContext);
  if (context === undefined) {
    throw new Error('useMilestone must be used within a MilestoneProvider');
  }
  return context;
}
