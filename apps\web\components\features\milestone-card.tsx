'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@workspace/ui/components/card';
import { Badge } from '@workspace/ui/components/badge';
import { Progress } from '@workspace/ui/components/progress';
import { Button } from '@workspace/ui/components/button';
import { Clock, Trophy, Lock, CheckCircle, Play } from 'lucide-react';
import { Milestone, MilestoneStatus } from '../../types';
import { useMilestone } from '../../lib/contexts/milestone-context';

interface MilestoneCardProps {
  milestone: Milestone;
  className?: string;
}

const statusConfig = {
  locked: {
    icon: Lock,
    color: 'bg-gray-100 text-gray-600 border-gray-200',
    badgeVariant: 'secondary' as const,
    buttonText: 'Locked',
    buttonDisabled: true,
  },
  unlocked: {
    icon: Play,
    color: 'bg-blue-50 text-blue-900 border-blue-200',
    badgeVariant: 'default' as const,
    buttonText: 'Start',
    buttonDisabled: false,
  },
  active: {
    icon: Play,
    color: 'bg-yellow-50 text-yellow-900 border-yellow-200 ring-2 ring-yellow-300',
    badgeVariant: 'default' as const,
    buttonText: 'Continue',
    buttonDisabled: false,
  },
  completed: {
    icon: CheckCircle,
    color: 'bg-green-50 text-green-900 border-green-200',
    badgeVariant: 'success' as const,
    buttonText: 'Completed',
    buttonDisabled: true,
  },
};

export function MilestoneCard({ milestone, className }: MilestoneCardProps) {
  const { setCurrentMilestone, updateMilestoneStatus } = useMilestone();
  const config = statusConfig[milestone.status];
  const StatusIcon = config.icon;

  const completedTasks = milestone.tasks.filter(task => task.status === 'completed').length;
  const totalTasks = milestone.tasks.length;
  const progressPercentage = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

  const handleCardClick = () => {
    if (milestone.status === 'unlocked' || milestone.status === 'active') {
      setCurrentMilestone(milestone.id);
      if (milestone.status === 'unlocked') {
        updateMilestoneStatus(milestone.id, 'active');
      }
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner':
        return 'bg-green-100 text-green-800';
      case 'intermediate':
        return 'bg-yellow-100 text-yellow-800';
      case 'advanced':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'self-discovery':
        return 'bg-purple-100 text-purple-800';
      case 'career-exploration':
        return 'bg-blue-100 text-blue-800';
      case 'skill-building':
        return 'bg-orange-100 text-orange-800';
      case 'networking':
        return 'bg-pink-100 text-pink-800';
      case 'job-search':
        return 'bg-indigo-100 text-indigo-800';
      case 'interview-prep':
        return 'bg-teal-100 text-teal-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      whileHover={{ scale: milestone.status !== 'locked' ? 1.02 : 1 }}
      className={className}
    >
      <Card 
        className={`cursor-pointer transition-all duration-200 ${config.color} ${
          milestone.status === 'locked' ? 'opacity-60' : 'hover:shadow-lg'
        }`}
        onClick={handleCardClick}
      >
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-2">
              <StatusIcon className="h-5 w-5" />
              <CardTitle className="text-lg">{milestone.title}</CardTitle>
            </div>
            <div className="flex gap-2">
              <Badge variant={config.badgeVariant} className="text-xs">
                {milestone.status.replace('_', ' ').toUpperCase()}
              </Badge>
            </div>
          </div>
          <CardDescription className="text-sm">
            {milestone.description}
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Progress Bar */}
          {milestone.status !== 'locked' && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Progress</span>
                <span>{completedTasks}/{totalTasks} tasks</span>
              </div>
              <Progress 
                value={progressPercentage} 
                className="h-2"
                variant={milestone.status === 'completed' ? 'milestone' : 'default'}
              />
            </div>
          )}

          {/* Metadata */}
          <div className="flex flex-wrap gap-2">
            <Badge variant="outline" className={getDifficultyColor(milestone.difficulty)}>
              {milestone.difficulty}
            </Badge>
            <Badge variant="outline" className={getCategoryColor(milestone.category)}>
              {milestone.category.replace('-', ' ')}
            </Badge>
          </div>

          {/* Stats */}
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <div className="flex items-center gap-1">
              <Clock className="h-4 w-4" />
              <span>{milestone.estimatedDuration} min</span>
            </div>
            <div className="flex items-center gap-1">
              <Trophy className="h-4 w-4" />
              <span>{milestone.xpReward} XP</span>
            </div>
          </div>

          {/* Action Button */}
          <Button
            className="w-full"
            disabled={config.buttonDisabled}
            variant={milestone.status === 'completed' ? 'secondary' : 'default'}
          >
            {config.buttonText}
          </Button>

          {/* Prerequisites */}
          {milestone.prerequisites.length > 0 && milestone.status === 'locked' && (
            <div className="text-xs text-muted-foreground">
              <p className="font-medium">Prerequisites:</p>
              <ul className="list-disc list-inside mt-1">
                {milestone.prerequisites.map((prereq, index) => (
                  <li key={index}>{prereq}</li>
                ))}
              </ul>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}
