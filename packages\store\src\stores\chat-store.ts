import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import type { ChatSession, ChatMessage, Agent, AgentType } from '@kaizen/types';

interface ChatState {
  sessions: ChatSession[];
  activeSession: ChatSession | null;
  agents: Agent[];
  isLoading: boolean;
  isTyping: boolean;
  error: string | null;
}

interface ChatActions {
  fetchAgents: () => Promise<void>;
  createSession: (agentId: string, context?: any) => Promise<string>;
  setActiveSession: (sessionId: string) => void;
  sendMessage: (content: string, sessionId?: string) => Promise<void>;
  receiveMessage: (message: ChatMessage) => void;
  setTyping: (isTyping: boolean) => void;
  endSession: (sessionId: string) => Promise<void>;
  clearError: () => void;
}

type ChatStore = ChatState & ChatActions;

export const useChatStore = create<ChatStore>()(
  immer((set, get) => ({
    // Initial state
    sessions: [],
    activeSession: null,
    agents: [],
    isLoading: false,
    isTyping: false,
    error: null,

    // Actions
    fetchAgents: async () => {
      set((state) => {
        state.isLoading = true;
        state.error = null;
      });

      try {
        // Mock API call - replace with actual API
        const response = await fetch('/api/agents');
        if (!response.ok) {
          throw new Error('Failed to fetch agents');
        }

        const agents = await response.json();

        set((state) => {
          state.agents = agents;
          state.isLoading = false;
        });
      } catch (error) {
        set((state) => {
          state.error = error instanceof Error ? error.message : 'Failed to fetch agents';
          state.isLoading = false;
        });
      }
    },

    createSession: async (agentId: string, context?: any) => {
      try {
        // Mock API call - replace with actual API
        const response = await fetch('/api/chat/sessions', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ agentId, context }),
        });

        if (!response.ok) {
          throw new Error('Failed to create chat session');
        }

        const session = await response.json();

        set((state) => {
          state.sessions.push(session);
          state.activeSession = session;
        });

        return session.id;
      } catch (error) {
        set((state) => {
          state.error = error instanceof Error ? error.message : 'Failed to create chat session';
        });
        throw error;
      }
    },

    setActiveSession: (sessionId: string) => {
      const session = get().sessions.find(s => s.id === sessionId);
      if (session) {
        set((state) => {
          state.activeSession = session;
        });
      }
    },

    sendMessage: async (content: string, sessionId?: string) => {
      const targetSessionId = sessionId || get().activeSession?.id;
      if (!targetSessionId) {
        throw new Error('No active session');
      }

      const userMessage: ChatMessage = {
        id: `msg_${Date.now()}`,
        chatId: targetSessionId,
        userId: 'current_user', // Replace with actual user ID
        content,
        type: 'text',
        timestamp: new Date(),
      };

      // Add user message immediately
      set((state) => {
        const session = state.sessions.find(s => s.id === targetSessionId);
        if (session) {
          session.messages.push(userMessage);
        }
        if (state.activeSession?.id === targetSessionId) {
          state.activeSession.messages.push(userMessage);
        }
      });

      try {
        // Mock API call - replace with actual API
        const response = await fetch(`/api/chat/sessions/${targetSessionId}/messages`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ content, type: 'text' }),
        });

        if (!response.ok) {
          throw new Error('Failed to send message');
        }

        // Set typing indicator
        set((state) => {
          state.isTyping = true;
        });

        // Simulate agent response (replace with actual WebSocket or polling)
        setTimeout(() => {
          const agentMessage: ChatMessage = {
            id: `msg_${Date.now() + 1}`,
            chatId: targetSessionId,
            agentId: get().activeSession?.agentId,
            content: 'This is a mock response from the agent.',
            type: 'text',
            timestamp: new Date(),
          };

          get().receiveMessage(agentMessage);
          get().setTyping(false);
        }, 2000);

      } catch (error) {
        set((state) => {
          state.error = error instanceof Error ? error.message : 'Failed to send message';
          state.isTyping = false;
        });
      }
    },

    receiveMessage: (message: ChatMessage) => {
      set((state) => {
        const session = state.sessions.find(s => s.id === message.chatId);
        if (session) {
          session.messages.push(message);
        }
        if (state.activeSession?.id === message.chatId) {
          state.activeSession.messages.push(message);
        }
      });
    },

    setTyping: (isTyping: boolean) => {
      set((state) => {
        state.isTyping = isTyping;
      });
    },

    endSession: async (sessionId: string) => {
      try {
        // Mock API call - replace with actual API
        const response = await fetch(`/api/chat/sessions/${sessionId}/end`, {
          method: 'POST',
        });

        if (!response.ok) {
          throw new Error('Failed to end session');
        }

        set((state) => {
          const session = state.sessions.find(s => s.id === sessionId);
          if (session) {
            session.status = 'completed';
          }
          if (state.activeSession?.id === sessionId) {
            state.activeSession = null;
          }
        });
      } catch (error) {
        set((state) => {
          state.error = error instanceof Error ? error.message : 'Failed to end session';
        });
      }
    },

    clearError: () => {
      set((state) => {
        state.error = null;
      });
    },
  }))
);
