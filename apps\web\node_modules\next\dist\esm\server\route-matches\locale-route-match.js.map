{"version": 3, "sources": ["../../../src/server/route-matches/locale-route-match.ts"], "sourcesContent": ["import type { LocaleRouteDefinition } from '../route-definitions/locale-route-definition'\nimport type { RouteMatch } from './route-match'\n\nexport interface LocaleRouteMatch<R extends LocaleRouteDefinition>\n  extends RouteMatch<R> {\n  readonly detectedLocale?: string\n}\n"], "names": [], "mappings": "AAGA,WAGC"}