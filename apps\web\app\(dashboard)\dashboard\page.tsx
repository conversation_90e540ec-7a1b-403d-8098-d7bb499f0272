'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@workspace/ui/components/card';
import { Button } from '@workspace/ui/components/button';
import { Badge } from '@workspace/ui/components/badge';
import { Progress } from '@workspace/ui/components/progress';
import { 
  Target, 
  MessageSquare, 
  TrendingUp, 
  Calendar,
  Clock,
  Trophy,
  Star,
  ArrowRight,
  Play,
  CheckCircle
} from 'lucide-react';
import { MilestoneCard } from '../../../components/features/milestone-card';
import { ChatInterface } from '../../../components/features/chat-interface';
import { GamificationPanel } from '../../../components/features/gamification-panel';
import { useMilestone } from '../../../lib/contexts/milestone-context';
import { useAuth } from '../../../lib/contexts/auth-context';
import { Milestone, MilestoneStatus } from '../../../types';

// Mock data - replace with real data from contexts/API
const mockMilestones: Milestone[] = [
  {
    id: '1',
    title: 'Self-Discovery Foundation',
    description: 'Understand your values, strengths, and career interests through guided assessments and reflections.',
    order: 1,
    status: 'completed',
    xpReward: 200,
    estimatedDuration: 45,
    prerequisites: [],
    tasks: [
      {
        id: '1-1',
        milestoneId: '1',
        title: 'Values Assessment',
        description: 'Identify your core values',
        type: 'quiz',
        order: 1,
        status: 'completed',
        xpReward: 50,
        estimatedDuration: 15,
        content: { instructions: 'Complete the values assessment' },
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: '1-2',
        milestoneId: '1',
        title: 'Strengths Reflection',
        description: 'Reflect on your key strengths',
        type: 'reflection',
        order: 2,
        status: 'completed',
        xpReward: 50,
        estimatedDuration: 20,
        content: { instructions: 'Write about your strengths' },
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ],
    category: 'self-discovery',
    difficulty: 'beginner',
    tags: ['assessment', 'reflection', 'foundation'],
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: '2',
    title: 'Career Exploration',
    description: 'Explore different career paths and understand industry trends and opportunities.',
    order: 2,
    status: 'active',
    xpReward: 300,
    estimatedDuration: 60,
    prerequisites: ['1'],
    tasks: [
      {
        id: '2-1',
        milestoneId: '2',
        title: 'Industry Research',
        description: 'Research your target industry',
        type: 'research',
        order: 1,
        status: 'completed',
        xpReward: 75,
        estimatedDuration: 30,
        content: { instructions: 'Research industry trends' },
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: '2-2',
        milestoneId: '2',
        title: 'Role Comparison',
        description: 'Compare different roles in your field',
        type: 'form',
        order: 2,
        status: 'in_progress',
        xpReward: 75,
        estimatedDuration: 30,
        content: { instructions: 'Compare roles using our tool' },
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ],
    category: 'career-exploration',
    difficulty: 'beginner',
    tags: ['research', 'exploration', 'industry'],
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: '3',
    title: 'Skill Building Plan',
    description: 'Create a personalized learning roadmap to develop the skills needed for your target role.',
    order: 3,
    status: 'unlocked',
    xpReward: 400,
    estimatedDuration: 90,
    prerequisites: ['2'],
    tasks: [],
    category: 'skill-building',
    difficulty: 'intermediate',
    tags: ['skills', 'learning', 'roadmap'],
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

export default function DashboardPage() {
  const { user } = useAuth();
  const { milestones, currentMilestone } = useMilestone();

  // Use mock data for now - replace with real data
  const displayMilestones = milestones.length > 0 ? milestones : mockMilestones;
  const activeMilestone = currentMilestone || displayMilestones.find(m => m.status === 'active');
  const nextMilestone = displayMilestones.find(m => m.status === 'unlocked');

  const completedMilestones = displayMilestones.filter(m => m.status === 'completed').length;
  const totalMilestones = displayMilestones.length;
  const overallProgress = (completedMilestones / totalMilestones) * 100;

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 18) return 'Good afternoon';
    return 'Good evening';
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-8"
    >
      {/* Welcome Section */}
      <motion.div variants={itemVariants}>
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {getGreeting()}, {user?.firstName}! 👋
          </h1>
          <p className="text-gray-600">
            Ready to continue your career journey? Let's make progress today.
          </p>
        </div>
      </motion.div>

      {/* Quick Stats */}
      <motion.div variants={itemVariants}>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="bg-blue-100 p-3 rounded-full">
                  <Target className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <div className="text-2xl font-bold">{completedMilestones}/{totalMilestones}</div>
                  <div className="text-sm text-gray-600">Milestones</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="bg-green-100 p-3 rounded-full">
                  <TrendingUp className="h-6 w-6 text-green-600" />
                </div>
                <div>
                  <div className="text-2xl font-bold">{Math.round(overallProgress)}%</div>
                  <div className="text-sm text-gray-600">Progress</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="bg-purple-100 p-3 rounded-full">
                  <Trophy className="h-6 w-6 text-purple-600" />
                </div>
                <div>
                  <div className="text-2xl font-bold">1,250</div>
                  <div className="text-sm text-gray-600">Total XP</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="bg-orange-100 p-3 rounded-full">
                  <Star className="h-6 w-6 text-orange-600" />
                </div>
                <div>
                  <div className="text-2xl font-bold">5</div>
                  <div className="text-sm text-gray-600">Day Streak</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </motion.div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Left Column - Current Progress */}
        <div className="lg:col-span-2 space-y-6">
          {/* Current Milestone */}
          {activeMilestone && (
            <motion.div variants={itemVariants}>
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Play className="h-5 w-5 text-blue-600" />
                    Continue Learning
                  </CardTitle>
                  <CardDescription>
                    Pick up where you left off
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <MilestoneCard milestone={activeMilestone} />
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Next Milestone */}
          {nextMilestone && (
            <motion.div variants={itemVariants}>
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5 text-green-600" />
                    Up Next
                  </CardTitle>
                  <CardDescription>
                    Your next milestone is ready
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <MilestoneCard milestone={nextMilestone} />
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Recent Activity */}
          <motion.div variants={itemVariants}>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5 text-purple-600" />
                  Recent Activity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <div className="flex-1">
                      <div className="font-medium">Completed Values Assessment</div>
                      <div className="text-sm text-gray-600">2 hours ago • +50 XP</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                    <Play className="h-5 w-5 text-blue-600" />
                    <div className="flex-1">
                      <div className="font-medium">Started Industry Research</div>
                      <div className="text-sm text-gray-600">1 day ago</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 p-3 bg-yellow-50 rounded-lg">
                    <Trophy className="h-5 w-5 text-yellow-600" />
                    <div className="flex-1">
                      <div className="font-medium">Earned "First Steps" Achievement</div>
                      <div className="text-sm text-gray-600">2 days ago • +100 XP</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Right Column - Sidebar */}
        <div className="space-y-6">
          {/* Chat Interface */}
          <motion.div variants={itemVariants}>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5 text-blue-600" />
                  Career Coach
                </CardTitle>
                <CardDescription>
                  Get personalized guidance
                </CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <ChatInterface agentId="coach-1" className="h-80" />
              </CardContent>
            </Card>
          </motion.div>

          {/* Gamification Panel */}
          <motion.div variants={itemVariants}>
            <GamificationPanel />
          </motion.div>

          {/* Quick Actions */}
          <motion.div variants={itemVariants}>
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button variant="outline" className="w-full justify-between">
                  Explore Careers
                  <ArrowRight className="h-4 w-4" />
                </Button>
                <Button variant="outline" className="w-full justify-between">
                  View Roadmap
                  <ArrowRight className="h-4 w-4" />
                </Button>
                <Button variant="outline" className="w-full justify-between">
                  Check Achievements
                  <ArrowRight className="h-4 w-4" />
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </motion.div>
  );
}
