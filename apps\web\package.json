{"name": "@kaizen/web", "version": "1.0.0", "private": true, "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "start": "next start", "type-check": "tsc --noEmit", "test": "jest", "test:coverage": "jest --coverage", "test:watch": "jest --watch", "clean": "rm -rf .next dist coverage"}, "dependencies": {"next": "^14.2.0", "react": "^18.3.0", "react-dom": "^18.3.0", "@tanstack/react-query": "^5.0.0", "@tanstack/react-query-devtools": "^5.0.0", "zustand": "^4.4.0", "react-hook-form": "^7.47.0", "@hookform/resolvers": "^3.3.0", "zod": "^3.22.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "class-variance-authority": "^0.7.0", "lucide-react": "^0.290.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "react-beautiful-dnd": "^13.1.1", "framer-motion": "^10.16.0", "react-intersection-observer": "^9.5.0", "next-intl": "^3.0.0", "@kaizen/ui": "workspace:*", "@kaizen/utils": "workspace:*", "@kaizen/types": "workspace:*", "@kaizen/api": "workspace:*", "@kaizen/store": "workspace:*"}, "devDependencies": {"@types/node": "^20.8.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/react-beautiful-dnd": "^13.1.0", "typescript": "^5.2.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "@kaizen/eslint-config": "workspace:*", "eslint": "^8.48.0", "eslint-config-next": "^14.2.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "@testing-library/react": "^14.0.0", "@testing-library/jest-dom": "^6.1.0", "@testing-library/user-event": "^14.5.0"}}