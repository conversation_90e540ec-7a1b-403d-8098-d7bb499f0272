{"version": 3, "sources": ["../../../src/shared/lib/amp-mode.ts"], "sourcesContent": ["export function isInAmpMode({\n  ampFirst = false,\n  hybrid = false,\n  hasQuery = false,\n} = {}): boolean {\n  return ampFirst || (hybrid && hasQuery)\n}\n"], "names": ["isInAmpMode", "ampFirs<PERSON>", "hybrid", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": "AAAA,OAAO,SAASA,YAAY;IAAA,IAAA,EAC1BC,WAAW,KAAK,EAChBC,SAAS,KAAK,EACdC,WAAW,KAAK,EACjB,GAJ2B,mBAIxB,CAAC,IAJuB;IAK1B,OAAOF,YAAaC,UAAUC;AAChC"}