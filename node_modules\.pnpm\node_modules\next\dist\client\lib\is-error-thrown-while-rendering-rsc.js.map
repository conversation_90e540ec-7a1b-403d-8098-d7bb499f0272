{"version": 3, "sources": ["../../../src/client/lib/is-error-thrown-while-rendering-rsc.ts"], "sourcesContent": ["export const shouldRenderRootLevelErrorOverlay = () => {\n  return !!window.__next_root_layout_missing_tags?.length\n}\n"], "names": ["shouldRenderRootLevelErrorOverlay", "window", "__next_root_layout_missing_tags", "length"], "mappings": ";;;;+BAAaA;;;eAAAA;;;AAAN,MAAMA,oCAAoC;QACtCC;IAAT,OAAO,CAAC,GAACA,0CAAAA,OAAOC,+BAA+B,qBAAtCD,wCAAwCE,MAAM;AACzD"}