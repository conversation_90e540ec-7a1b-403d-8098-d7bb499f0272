{"name": "kaizen-project", "version": "1.0.0", "private": true, "description": "The Kaizen Project - Career Coaching Platform", "scripts": {"build": "cd apps/web && npm run build", "dev": "cd apps/web && npm run dev", "lint": "cd apps/web && npm run lint", "test": "cd apps/web && npm run test", "test:coverage": "cd apps/web && npm run test:coverage", "type-check": "cd apps/web && npm run type-check", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "install-all": "npm install && cd apps/web && npm install"}, "devDependencies": {"prettier": "^3.0.0", "eslint": "^8.48.0", "typescript": "^5.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}