{"name": "kaizen-project", "version": "1.0.0", "private": true, "description": "The Kaizen Project - Career Coaching Platform", "workspaces": ["apps/*", "packages/*"], "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "test": "turbo run test", "test:coverage": "turbo run test:coverage", "type-check": "turbo run type-check", "clean": "turbo run clean", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "storybook": "turbo run storybook", "build-storybook": "turbo run build-storybook"}, "devDependencies": {"@turbo/gen": "^1.10.12", "turbo": "^1.10.12", "prettier": "^3.0.0", "eslint": "^8.48.0", "typescript": "^5.2.2"}, "packageManager": "npm@9.8.1", "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}